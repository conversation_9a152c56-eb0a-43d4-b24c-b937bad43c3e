.spinWrapper {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
}

.fullPageLoading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.loadingContent {
  text-align: center;
  z-index: 2;
  position: relative;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}

.loadingSpinner {
  display: flex;
  gap: 8px;
  align-items: center;
}

.spinner {
  width: 12px;
  height: 12px;
  background: #fff;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.spinner:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner:nth-child(2) {
  animation-delay: -0.16s;
}

.spinner:nth-child(3) {
  animation-delay: 0s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loadingText {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  animation: fadeInUp 0.6s ease-out;
}

.loadingSubtext {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.backgroundAnimation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 60%;
  left: 80%;
  animation-delay: -2s;
}

.particle:nth-child(3) {
  width: 100px;
  height: 100px;
  top: 80%;
  left: 20%;
  animation-delay: -4s;
}

.particle:nth-child(4) {
  width: 40px;
  height: 40px;
  top: 30%;
  left: 70%;
  animation-delay: -1s;
}

.particle:nth-child(5) {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 60%;
  animation-delay: -3s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loadingText {
    font-size: 16px;
  }

  .loadingSubtext {
    font-size: 12px;
  }

  .particle {
    display: none;
  }
}
