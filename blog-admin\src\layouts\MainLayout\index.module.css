/* 整体布局 */
.layoutRoot {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 侧边栏Logo */
.siderLogo {
  height: 64px;
  margin: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  text-align: center;
  line-height: 64px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 12px;
  letter-spacing: 1px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.siderLogo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.siderLogo:hover::before {
  left: 100%;
}

/* 炫酷Header */
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Header左侧内容区域 */
.headerLeft {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* 允许收缩 */
  max-width: calc(100% - 300px); /* 为右侧用户区域预留空间 */
}

.headerTitle {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: fadeInLeft 0.6s ease-out;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 紧凑的统计卡片样式 */
.headerStats {
  display: flex;
  align-items: center;
  gap: 8px;
  animation: fadeInRight 0.6s ease-out 0.2s both;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.08);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  min-width: 45px;
  max-width: 50px;
  animation: fadeInUp 0.6s ease-out both;
}

.statItem:nth-child(1) {
  animation-delay: 0.1s;
}
.statItem:nth-child(2) {
  animation-delay: 0.2s;
}
.statItem:nth-child(3) {
  animation-delay: 0.3s;
}
.statItem:nth-child(4) {
  animation-delay: 0.4s;
}

.statItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.statItem:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.2);
}

.statItem:hover::before {
  opacity: 1;
}

.statIcon {
  font-size: 12px;
  color: #667eea;
  margin-bottom: 2px;
  padding: 3px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.statItem:hover .statIcon {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.05);
}

.statNumber {
  font-size: 11px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 1px;
}

.statLabel {
  font-size: 8px;
  color: #64748b;
  font-weight: 500;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 用户信息区域样式 */

/* Header右侧用户区域 */
.headerRight {
  display: flex;
  align-items: center;
  gap: 16px;
  animation: fadeInRight 0.6s ease-out 0.4s both;
  flex-shrink: 0; /* 不允许收缩 */
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.userInfo:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.userDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.userName {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.2;
}

.userRole {
  font-size: 12px;
  color: #667eea;
  line-height: 1.2;
}

.avatar {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(102, 126, 234, 0.2);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.avatar:hover {
  transform: scale(1.1);
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* 侧边栏样式优化 */
.sider {
  background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.sider::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, #667eea, #764ba2);
  opacity: 0.5;
}

/* 内容区域 */
.innerContent {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 64px);
  position: relative;
  overflow: hidden;
}

.innerContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  pointer-events: none;
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-5px) rotate(0.5deg);
  }
  66% {
    transform: translateY(3px) rotate(-0.5deg);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .headerLeft {
    max-width: calc(100% - 250px);
  }

  .headerStats {
    gap: 6px;
  }

  .statItem {
    min-width: 40px;
    max-width: 45px;
    padding: 5px 6px;
  }

  .statIcon {
    font-size: 10px;
    padding: 2px;
  }

  .statNumber {
    font-size: 10px;
  }

  .statLabel {
    font-size: 7px;
  }
}

@media (max-width: 992px) {
  .headerLeft {
    gap: 10px;
    max-width: calc(100% - 200px);
  }

  .headerStats {
    gap: 4px;
  }

  .statItem:nth-child(4) {
    display: none; /* 隐藏第4个统计项 */
  }
}

@media (max-width: 768px) {
  .headerLeft {
    gap: 8px;
    max-width: calc(100% - 180px);
  }

  .headerTitle {
    font-size: 16px;
  }

  .statItem:nth-child(3) {
    display: none; /* 隐藏第3个统计项 */
  }
}

@media (max-width: 576px) {
  .headerStats {
    display: none; /* 小屏幕完全隐藏统计 */
  }

  .headerLeft {
    gap: 12px;
    max-width: calc(100% - 150px);
  }
}
