/* 日志管理页面样式 */

.logContainer {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  animation: fadeIn 0.6s ease-out;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.statsCard {
  margin-bottom: 24px;
}

.statsCard .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.statsCard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.toolbarCard {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.toolbarCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.fileListCard {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.fileListCard:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.logTable .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  font-weight: 600;
  color: #495057;
}

.logTable .ant-table-tbody > tr:hover > td {
  background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
}

.logDrawer .ant-drawer-content {
  background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
}

.statsModal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.cleanModal .ant-modal-content {
  border-radius: 12px;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.fileListCard .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.logViewer {
  height: 70vh;
  overflow-y: auto;
  background: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.logViewer.lightTheme {
  background: #ffffff;
  color: #333333;
  border: 1px solid #d9d9d9;
}

.logViewer.darkTheme {
  background: #1e1e1e;
  color: #d4d4d4;
}

.logEntry {
  display: flex;
  align-items: flex-start;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: background-color 0.2s;
}

.logEntry:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.lightTheme .logEntry {
  border-bottom: 1px solid #f0f0f0;
}

.lightTheme .logEntry:hover {
  background-color: #f9f9f9;
}

.logTimestamp {
  flex-shrink: 0;
  width: 180px;
  color: #888;
  font-size: 12px;
  margin-right: 12px;
}

.logLevel {
  flex-shrink: 0;
  width: 60px;
  margin-right: 12px;
  text-align: center;
  font-weight: 600;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
}

.logLevel.error {
  background: #ff4d4f;
  color: white;
}

.logLevel.warn {
  background: #faad14;
  color: white;
}

.logLevel.info {
  background: #1890ff;
  color: white;
}

.logLevel.debug {
  background: #722ed1;
  color: white;
}

.logLevel.verbose {
  background: #8c8c8c;
  color: white;
}

.logMessage {
  flex: 1;
  word-break: break-word;
  white-space: pre-wrap;
}

.logMeta {
  margin-top: 4px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 12px;
  color: #999;
}

.lightTheme .logMeta {
  background: #f5f5f5;
  color: #666;
}

.searchHighlight {
  background: #fadb14;
  color: #000;
  padding: 1px 2px;
  border-radius: 2px;
}

.filterPanel {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filterPanel .ant-form-item {
  margin-bottom: 12px;
}

.logViewerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.logViewerControls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.realtimeIndicator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #52c41a;
  font-size: 12px;
}

.realtimeIndicator.inactive {
  color: #8c8c8c;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.emptyState {
  text-align: center;
  padding: 40px;
  color: #999;
}

.emptyState .ant-empty-description {
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logContainer {
    padding: 16px;
  }

  .logViewer {
    height: 50vh;
    font-size: 12px;
  }

  .logTimestamp {
    width: 120px;
    font-size: 11px;
  }

  .logLevel {
    width: 50px;
    font-size: 10px;
  }
}

/* 动画效果 */
.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideIn {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 滚动条样式 */
.logViewer::-webkit-scrollbar {
  width: 8px;
}

.logViewer::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.logViewer::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.logViewer::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
