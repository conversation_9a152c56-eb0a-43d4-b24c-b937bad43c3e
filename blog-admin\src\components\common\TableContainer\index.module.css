.tableContainer {
  border-radius: 16px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
}

.tableContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tableContainer:hover {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.tableContainer:hover::before {
  opacity: 1;
}

.tableWrapper {
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
}

/* 表格滚动配置 - 允许横向滚动但优化滚动条样式 */
.tableWrapper :global(.ant-table-body) {
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

.tableWrapper :global(.ant-table-tbody) {
  overflow: visible !important;
}

/* 允许表格横向滚动 */
.tableWrapper :global(.ant-table) {
  overflow: visible !important;
}

.tableWrapper :global(.ant-table-content) {
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

/* TableWrapper 滚动条样式 */
.tableWrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tableWrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.tableWrapper::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.tableWrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.6);
}

/* 表格样式优化 */
.tableWrapper :global(.ant-table) {
  border-radius: 0;
  background: transparent;
}

.tableWrapper :global(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
  font-weight: 700;
  color: #1a202c;
  padding: 20px 16px;
  font-size: 14px;
  position: relative;
  cursor: default !important; /* 移除光标 */
}

.tableWrapper :global(.ant-table-thead > tr > th:hover::before) {
  opacity: 1;
}

/* 移除表头排序相关的光标样式 */
.tableWrapper :global(.ant-table-thead > tr > th .ant-table-column-sorters) {
  cursor: default !important;
}

.tableWrapper :global(.ant-table-thead > tr > th .ant-table-column-title) {
  cursor: default !important;
}

/* 隐藏表头的列调整竖线 */
.tableWrapper :global(.ant-table-thead > tr > th::after) {
  display: none !important;
}

.tableWrapper :global(.ant-table-thead > tr > th .ant-table-column-sorters::after) {
  display: none !important;
}

/* 移除表头的resize功能 */
.tableWrapper :global(.ant-table-thead > tr > th .ant-table-resize-handle) {
  display: none !important;
}

.tableWrapper :global(.ant-table-tbody > tr > td) {
  padding: 18px 16px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  font-size: 14px;
}

.tableWrapper :global(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.tableWrapper :global(.ant-table-tbody > tr:last-child > td) {
  border-bottom: none;
}

/* 分页样式优化 */
.tableWrapper :global(.ant-pagination) {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  margin: 0;
  text-align: right;
  position: relative;
}

.tableWrapper :global(.ant-pagination::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.tableWrapper :global(.ant-pagination-total-text) {
  color: #667eea;
  font-size: 13px;
  font-weight: 500;
}

.tableWrapper :global(.ant-pagination-item) {
  border-radius: 8px;
  border-color: rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.tableWrapper :global(.ant-pagination-item:hover) {
  border-color: #667eea;
  transform: translateY(-1px);
}

.tableWrapper :global(.ant-pagination-item-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.tableWrapper :global(.ant-pagination-item-active a) {
  color: #fff;
}

/* 空状态样式 */
.tableWrapper :global(.ant-empty) {
  padding: 40px 20px;
}

.tableWrapper :global(.ant-empty-description) {
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tableContainer {
    margin: 0 -8px;
    border-radius: 8px;
  }

  .tableWrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  }

  .tableWrapper :global(.ant-table-thead > tr > th),
  .tableWrapper :global(.ant-table-tbody > tr > td) {
    padding: 12px 8px;
    font-size: 12px;
    white-space: nowrap; /* 防止文字换行 */
  }

  .tableWrapper :global(.ant-pagination) {
    padding: 12px 16px;
    text-align: center;
  }

  /* 确保操作按钮在小屏幕上可见 */
  .tableWrapper :global(.ant-table-tbody > tr > td:last-child) {
    position: sticky;
    right: 0;
    background: #fff;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  .tableWrapper :global(.ant-table-thead > tr > th:last-child) {
    position: sticky;
    right: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    z-index: 2;
  }
}
