/* LogStats 组件样式 */

.logStatsContainer {
  padding: 0;
}

.statCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.statCard .ant-statistic-title {
  color: #666;
  font-weight: 500;
}

.statCard .ant-statistic-content {
  font-weight: 600;
}

.distributionCard,
.recentCard,
.healthCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.distributionCard:hover,
.recentCard:hover,
.healthCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.typeDistribution {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.typeItem {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.typeItem:hover {
  background: #f0f0f0;
  transform: translateX(4px);
}

.typeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.recentItem {
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.recentItem:hover {
  background: #f9f9f9;
}

.recentContent {
  width: 100%;
}

.recentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.healthCard {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f5ff 100%);
}

.healthItem {
  text-align: center;
  padding: 16px 8px;
  position: relative;
}

.healthValue {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.healthLabel {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.healthIndicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto;
  position: relative;
  animation: pulse 2s infinite;
}

.healthIndicator.good {
  background: #52c41a;
  box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
}

.healthIndicator.warning {
  background: #faad14;
  box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.4);
}

.healthIndicator.error {
  background: #ff4d4f;
  box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
}

.healthIndicator.info {
  background: #1890ff;
  box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.healthIndicator.warning {
  animation-name: pulseWarning;
}

@keyframes pulseWarning {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(250, 173, 20, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0);
  }
}

.healthIndicator.error {
  animation-name: pulseError;
}

@keyframes pulseError {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

.healthIndicator.info {
  animation-name: pulseInfo;
}

@keyframes pulseInfo {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .typeDistribution {
    gap: 12px;
  }

  .typeItem {
    padding: 8px;
  }

  .healthItem {
    padding: 12px 4px;
  }

  .healthValue {
    font-size: 16px;
  }

  .healthLabel {
    font-size: 11px;
  }
}
