/* 页面布局通用样式 */
.pageContainer {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 64px);
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.pageContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(240, 147, 251, 0.05) 0%, transparent 50%);
  pointer-events: none;
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.pageContent {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(102, 126, 234, 0.1);
  padding: 32px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.pageContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 页面标题区域 */
.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.pageTitle {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.pageBreadcrumb {
  color: #8c8c8c;
  font-size: 14px;
}

/* 卡片间距 */
.cardSpacing {
  margin-bottom: 16px;
}

.cardSpacing:last-child {
  margin-bottom: 0;
}

/* 搜索区域动画 */
.searchSection {
  transition: all 0.3s ease;
  transform-origin: top;
}

.searchSection.collapsed {
  transform: scaleY(0);
  opacity: 0;
  margin-bottom: 0;
  overflow: hidden;
}

/* 操作栏样式 */
.toolbarSection {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #f5f5f5;
  padding: 8px 0;
  margin: -8px 0 16px 0;
}

/* 表格区域 */
.tableSection {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pageContainer {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .pageContainer {
    padding: 12px;
  }

  .pageHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .pageTitle {
    font-size: 18px;
  }

  .toolbarSection {
    position: static;
    margin: 0 0 16px 0;
  }
}

@media (max-width: 480px) {
  .pageContainer {
    padding: 8px;
  }

  .pageTitle {
    font-size: 16px;
  }
}
