name: CI/CD Deploy

on:
  push:
    branches: ["main"] # 只在 main 分支 push 时触发

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install dependencies
        run: npm install

      - name: Build project
        run: npm run build
      
      # - name: Deploy to Server via SSH
      #   uses: appleboy/ssh-action@master
      #   with:
      #     host: ${{ secrets.SERVER_IP }}
      #     username: ${{ secrets.SSH_USERNAME }}
      #     key: ${{ secrets.SSH_PRIVATE_KEY }}
      #     ssh_options: "-o StrictHostKeyChecking=no"
      #     source: "./build/*"
      #     target: ${{ secrets.TARGET_DIR }}
      #     strip_components: 1
      #     overwrite: true
      #     script: |
      #       cd ${{ secrets.TARGET_DIR }}
      #       echo "正在部署..."
      #       ls -la
      #       # pm2 restart your_react_app

      - name: Deploy to Server via SSH
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          ssh_options: "-o StrictHostKeyChecking=no"
          source: "./build/*"
          target: ${{ secrets.TARGET_DIR }}
          strip_components: 1
          overwrite: true
          

      # - name: Test SSH Connection
      #   run: ssh -v ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_IP }} "echo Connected successfully"
      # - name: Upload files via rsync
      #   run: |
      #     rsync -avz -e "ssh -o StrictHostKeyChecking=no" ./build/ ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_IP }}:${{ secrets.TARGET_DIR }}/
