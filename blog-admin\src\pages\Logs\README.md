# 日志管理模块

## 功能概述

日志管理模块是一个功能强大、界面炫酷的系统日志查看和管理工具，提供了完整的日志文件管理功能。

## 主要功能

### 📊 统计信息展示

- **实时统计卡片**：显示总文件数、总大小、错误日志数、系统日志数
- **文件类型分布**：可视化展示不同类型日志文件的分布情况
- **健康状态指示器**：实时监控系统日志健康状态
- **最近日志文件**：显示最近修改的日志文件列表

### 📁 文件管理

- **文件列表展示**：表格形式展示所有日志文件
- **文件类型分类**：支持错误、认证、业务、系统四种类型
- **文件大小格式化**：自动转换为易读的文件大小格式
- **修改时间显示**：显示文件最后修改时间

### 👁️ 日志查看器

- **实时日志查看**：支持实时刷新和自动滚动
- **多主题支持**：浅色和深色两种主题
- **语法高亮**：不同日志级别使用不同颜色标识
- **搜索功能**：支持关键词搜索和高亮显示
- **级别过滤**：可按日志级别进行过滤
- **全屏模式**：支持全屏查看日志内容

### 🔧 高级功能

- **文件下载**：支持单个文件下载
- **批量清理**：可按天数批量清理过期日志
- **权限控制**：基于角色的权限管理
- **响应式设计**：适配各种屏幕尺寸

## 技术特色

### 🎨 炫酷界面

- **渐变背景**：使用CSS渐变创建现代化背景
- **毛玻璃效果**：backdrop-filter实现毛玻璃卡片效果
- **动画过渡**：丰富的CSS动画和过渡效果
- **悬停效果**：鼠标悬停时的动态反馈

### ⚡ 性能优化

- **懒加载**：组件和路由的懒加载
- **虚拟滚动**：大量日志数据的虚拟滚动
- **防抖搜索**：搜索输入的防抖处理
- **缓存机制**：API响应的智能缓存

### 🔒 安全特性

- **权限验证**：基于菜单权限的访问控制
- **文件类型检查**：只允许访问.log文件
- **路径安全**：防止路径遍历攻击
- **操作确认**：危险操作需要用户确认

## 组件架构

### 页面组件

- `Logs/index.tsx` - 主页面组件
- `Logs/index.module.css` - 页面样式

### 子组件

- `LogViewer` - 日志内容查看器
- `LogStats` - 统计信息卡片
- `LogLevelTag` - 日志级别标签
- `LogForm` - 日志配置表单

### API模块

- `api/logs.ts` - 日志相关API封装

### 类型定义

- `types/log.ts` - 日志相关TypeScript类型

## 使用方法

### 基本操作

1. **查看日志列表**：进入日志管理页面即可看到所有日志文件
2. **查看日志内容**：点击文件列表中的"查看内容"按钮
3. **搜索日志**：在查看器中输入关键词进行搜索
4. **过滤日志**：选择特定的日志级别进行过滤
5. **下载日志**：点击"下载文件"按钮下载日志文件

### 高级操作

1. **实时刷新**：开启实时模式自动刷新日志内容
2. **全屏查看**：点击全屏按钮获得更好的查看体验
3. **清理日志**：使用清理功能删除过期的日志文件
4. **查看统计**：点击"详细统计"查看完整的统计信息

## 配置选项

### 查看器配置

- 主题：浅色/深色
- 字体大小：10-24px
- 行高：1-3倍
- 显示选项：行号、时间戳、级别、元数据
- 自动换行：开启/关闭

### 实时更新配置

- 刷新间隔：1-60秒
- 最大条目数：10-1000条
- 自动滚动：开启/关闭
- 级别过滤：选择要显示的级别

### 清理配置

- 保留天数：1-365天
- 文件类型：选择要清理的类型
- 大小限制：按文件大小过滤

## 权限要求

- **读取权限**：查看日志文件和内容
- **下载权限**：下载日志文件
- **删除权限**：清理过期日志文件

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 注意事项

1. **性能考虑**：大型日志文件可能影响加载速度
2. **权限控制**：确保用户具有相应的操作权限
3. **文件安全**：只能访问指定目录下的.log文件
4. **清理操作**：清理操作不可恢复，请谨慎使用

## 未来规划

- [ ] 支持更多日志格式
- [ ] 添加日志分析功能
- [ ] 实现日志告警机制
- [ ] 支持日志导出为多种格式
- [ ] 添加日志图表可视化
- [ ] 实现日志内容的正则表达式搜索
