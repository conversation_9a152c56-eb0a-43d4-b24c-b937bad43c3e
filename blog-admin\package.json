{"name": "blog-admin", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.19", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "antd": "^5.26.2", "axios": "^1.10.0", "echarts-for-react": "^3.0.2", "react": "^18.2.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "react-scripts start", "build": "react-scripts build", "build:prod": "env-cmd -f .env.production react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "prepare": "husky"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "react-hooks/exhaustive-deps": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}]}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/axios": "^0.9.36", "@types/react-router-dom": "^5.3.3", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,scss,md}": ["prettier --write"]}, "proxy": "http://************:3000"}