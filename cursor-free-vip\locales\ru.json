{"menu": {"title": "Доступные опции", "exit": "Выйти из программы", "reset": "Сбросить ID машины", "register": "Зарегистрировать новый аккаунт Cursor", "register_google": "Зарегистрироваться через Google", "register_github": "Зарегистрироваться через GitHub", "register_manual": "Зарегистрировать Cursor используя свою почту", "quit": "Закрыть приложение Cursor", "select_language": "Выбрать язык", "input_choice": "Пожалуйста, введите ваш выбор ({choices})", "invalid_choice": "Неверный выбор. Пожалуйста, введите число из {choices}", "program_terminated": "Программа была завершена пользователем", "error_occurred": "Произошла ошибка: {error}. Пожалуйста, попробуйте снова", "press_enter": "Нажмите Enter для выхода", "disable_auto_update": "Отключить автоматическое обновление Cursor", "lifetime_access_enabled": "ВКЛЮЧЕН ПОЖИЗНЕННЫЙ ДОСТУП", "totally_reset": "Полностью сбросить Cursor", "outdate": "Устаревший", "temp_github_register": "Временная регистрация GitHub", "coming_soon": "Скоро", "fixed_soon": "Скоро будет исправлено", "contribute": "Внести вклад в проект", "config": "Показать конфигурацию", "delete_google_account": "Удалить Google аккаунт Cursor", "continue_prompt": "Продолжить? (y/N): ", "operation_cancelled_by_user": "Операция отменена пользователем", "exiting": "Выход ......", "bypass_version_check": "Пропустить проверку версии Cursor", "check_user_authorized": "Проверить авторизацию пользователя", "select_chrome_profile": "Выбрать профиль Chrome", "bypass_token_limit": "Обход ограничения токенов", "restore_machine_id": "Восстановить ID устройства из резервной копии", "admin_required": "Запуск в качестве исполняемого файла требуются привилегии администратора.", "language_config_saved": "Языковая конфигурация успешно сохранилась", "lang_invalid_choice": "Неверный выбор. Пожалуйста, введите один из следующих вариантов: ({lang_choices})", "manual_custom_auth": "Ручная пользовательская аут", "admin_required_continue": "Продолжение без привилегий администратора."}, "languages": {"ar": "Арабский", "en": "Английский", "zh_cn": "Упрощенный китайский", "zh_tw": "Традиционный китайский", "vi": "Вьетнамский", "nl": "Нидерландский", "de": "Немецкий", "fr": "Французский", "pt": "Бразильский португальский", "ru": "Русский", "es": "Испанский", "bg": "болгарский", "tr": "турецкий", "it": "Итальянский", "ja": "Японский"}, "quit_cursor": {"start": "Начало закрытия Cursor", "no_process": "Нет запущенных процессов Cursor", "terminating": "Завершение процесса {pid}", "waiting": "Ожидание завершения процесса", "success": "Все процессы Cursor закрыты", "timeout": "Таймаут процесса: {pids}", "error": "Произошла ошибка: {error}"}, "reset": {"title": "Инструмент сброса ID машины Cursor", "checking": "Проверка конфигурационного файла", "not_found": "Конфигурационный файл не найден", "no_permission": "Невозможно прочитать или записать конфигурационный файл, проверьте права доступа", "reading": "Чтение текущей конфигурации", "creating_backup": "Создание резервной копии конфигурации", "backup_exists": "Резервный файл уже существует, пропускаем шаг резервного копирования", "generating": "Генерация нового ID машины", "saving_json": "Сохранение новой конфигурации в JSON", "success": "ID машины успешно сброшен", "new_id": "Новый ID машины", "permission_error": "Ошибка прав доступа: {error}", "run_as_admin": "Пожалуйста, запустите программу от имени администратора", "process_error": "Ошибка процесса сброса: {error}", "updating_sqlite": "Обновление базы данных SQLite", "updating_pair": "Обновление пары ключ-значение", "sqlite_success": "База данных SQLite успешно обновлена", "sqlite_error": "Ошибка обновления базы данных SQLite: {error}", "press_enter": "Нажмите Enter для выхода", "unsupported_os": "Неподдерживаемая ОС: {os}", "linux_path_not_found": "Путь Linux не найден", "updating_system_ids": "Обновление системных ID", "system_ids_updated": "Системные ID успешно обновлены", "system_ids_update_failed": "Ошибка обновления системных ID: {error}", "windows_guid_updated": "Windows GUID успешно обновлен", "windows_permission_denied": "Отказано в доступе Windows", "windows_guid_update_failed": "Ошибка обновления Windows GUID", "macos_uuid_updated": "macOS UUID успешно обновлен", "plutil_command_failed": "Ошибка команды plutil", "start_patching": "Начало патчинга getMachineId", "macos_uuid_update_failed": "Ошибка обновления macOS UUID", "current_version": "Текущая версия Cursor: {version}", "patch_completed": "Патчинг getMachineId завершен", "patch_failed": "Ошибка патчинга getMachineId: {error}", "version_check_passed": "Проверка версии Cursor пройдена", "file_modified": "Файл изменен", "version_less_than_0_45": "Версия Cursor < 0.45.0, пропускаем патчинг getMachineId", "detecting_version": "Определение версии Cursor", "patching_getmachineid": "Патчинг getMachineId", "version_greater_than_0_45": "Версия Cursor >= 0.45.0, патчинг getMachineId", "permission_denied": "Отказано в доступе: {error}", "backup_created": "Резервная копия создана", "update_success": "Обновление успешно", "update_failed": "Ошибка обновления: {error}", "windows_machine_guid_updated": "Windows Machine GUID успешно обновлен", "reading_package_json": "Чтение package.json {path}", "invalid_json_object": "Неверный JSON объект", "no_version_field": "Поле версии не найдено в package.json", "version_field_empty": "Поле версии пусто", "invalid_version_format": "Неверный формат версии: {version}", "found_version": "Найдена версия: {version}", "version_parse_error": "Ошибка разбора версии: {error}", "package_not_found": "Package.json не найден: {path}", "check_version_failed": "Ошибка проверки версии: {error}", "stack_trace": "Трассировка стека", "version_too_low": "Версия Cursor слишком низкая: {version} < 0.45.0", "update_windows_machine_id_failed": "Обновить идентификатор машины Windows Fail: {error}", "path_not_found": "Путь не найден: {path}", "windows_machine_id_updated": "Идентификатор машины Windows успешно обновлен", "update_windows_machine_guid_failed": "Обновление Windows Machine Guide не удалось: {ошибка}", "no_write_permission": "Нет разрешения на запись: {path}", "file_not_found": "Файл не найден: {path}", "modify_file_failed": "Изменить файл не удастся: {ошибка}"}, "register": {"title": "Инструмент регистрации Cursor", "start": "Запуск процесса регистрации...", "handling_turnstile": "Обработка проверки безопасности...", "retry_verification": "Повторная попытка проверки...", "detect_turnstile": "Проверка безопасности...", "verification_success": "Проверка безопасности успешна", "starting_browser": "Открытие браузера...", "form_success": "Форма успешно отправлена", "browser_started": "Браузер успешно открыт", "waiting_for_second_verification": "Ожидание проверки email...", "waiting_for_verification_code": "Ожидание кода подтверждения...", "password_success": "Пароль успешно установлен", "password_error": "Не удалось установить пароль: {error}. Пожалуйста, попробуйте снова", "waiting_for_page_load": "Загрузка страницы...", "first_verification_passed": "Первичная проверка успешна", "mailbox": "Успешный доступ к почтовому ящику", "register_start": "Начать регистрацию", "form_submitted": "Форма отправлена, начало проверки...", "filling_form": "Заполнение формы", "visiting_url": "Переход по URL", "basic_info": "Основная информация отправлена", "handle_turnstile": "Обработка Turnstile", "no_turnstile": "Turnstile не обнаружен", "turnstile_passed": "Turnstile пройден", "verification_start": "Начало получения кода подтверждения", "verification_timeout": "Таймаут получения кода подтверждения", "verification_not_found": "Код подтверждения не найден", "try_get_code": "Попытка | {attempt} Получение кода подтверждения | Осталось времени: {time}с", "get_account": "Получение информации об аккаунте", "get_token": "Получение токена сессии Cursor", "token_success": "Токен успешно получен", "token_attempt": "Попытка | {attempt} раз получить токен | Повторная попытка через {time}с", "token_max_attempts": "Достигнуто максимальное количество попыток ({max}) | Не удалось получить токен", "token_failed": "Ошибка получения токена: {error}", "account_error": "Ошибка получения информации об аккаунте: {error}", "press_enter": "Нажмите Enter для выхода", "browser_start": "Запуск браузера", "open_mailbox": "Открытие страницы почтового ящика", "email_error": "Не удалось получить email адрес", "setup_error": "Ошибка настройки email: {error}", "start_getting_verification_code": "Начало получения кода подтверждения, попытка через 60с", "get_verification_code_timeout": "Таймаут получения кода подтверждения", "get_verification_code_success": "Код подтверждения успешно получен", "try_get_verification_code": "Попытка | {attempt} Получение кода подтверждения | Осталось времени: {remaining_time}с", "verification_code_filled": "Код подтверждения введен", "login_success_and_jump_to_settings_page": "Успешный вход и переход на страницу настроек", "detect_login_page": "Обнаружена страница входа, начало входа...", "cursor_registration_completed": "Регистрация Cursor завершена!", "set_password": "Установка пароля", "basic_info_submitted": "Основная информация отправлена", "cursor_auth_info_updated": "Информация авторизации Cursor обновлена", "cursor_auth_info_update_failed": "Ошибка обновления информации авторизации Cursor", "reset_machine_id": "Сброс ID машины", "account_info_saved": "Информация об аккаунте сохранена", "save_account_info_failed": "Ошибка сохранения информации об аккаунте", "get_email_address": "Получение email адреса", "update_cursor_auth_info": "Обновление информации авторизации Cursor", "register_process_error": "Ошибка процесса регистрации: {error}", "setting_password": "Установка пароля", "manual_code_input": "Ручной ввод кода", "manual_email_input": "Ручной ввод email", "password": "Пароль", "first_name": "Имя", "last_name": "Фамилия", "exit_signal": "Сигнал выхода", "email_address": "<PERSON><PERSON> адре<PERSON>", "config_created": "Конфигурация создана", "verification_failed": "Проверка не пройдена", "verification_error": "Ошибка проверки: {error}", "config_option_added": "Опция конфигурации добавлена: {option}", "config_updated": "Конфигурация обновлена", "password_submitted": "Пароль отправлен", "total_usage": "Общее использование: {usage}", "setting_on_password": "Установка пароля", "getting_code": "Получение кода подтверждения, попытка через 60с", "using_browser": "Использование {браузер} браузер: {path}", "could_not_track_processes": "Не удалось отслеживать {браузер} процессы: {ошибка}", "try_install_browser": "Попробуйте установить браузер с помощью менеджера пакета", "tempmail_plus_verification_started": "Начальный процесс проверки TempmailPlus", "max_retries_reached": "Максимальные попытки повторения достигли. Регистрация не удалась.", "tempmail_plus_enabled": "TempmailPlus включен", "browser_path_invalid": "{браузер} путь недействителен, используя путь по умолчанию", "human_verify_error": "Не могу проверить, что пользователь - это человек. Повторение ...", "using_tempmail_plus": "Использование TempmailPlus для проверки электронной почты", "tracking_processes": "Отслеживание {count} {браузер} процессы", "tempmail_plus_epin_missing": "Tempmailplus epin не настроен", "tempmail_plus_verification_failed": "TempmailPlus Проверка не удалась: {ошибка}", "using_browser_profile": "Использование {браузер} профиль из: {user_data_dir}", "tempmail_plus_verification_completed": "TempmailPlus проверка завершена успешно", "tempmail_plus_email_missing": "Электронная почта TempmailPlus не настроена", "tempmail_plus_config_missing": "Конфигурация TempmailPlus отсутствует", "tempmail_plus_init_failed": "Не удалось инициализировать TempmailPlus: {ошибка}", "tempmail_plus_initialized": "TempmailPlus инициализирован успешно", "tempmail_plus_disabled": "TempmailPlus отключен", "no_new_processes_detected": "Нет новых {браузер} процессов, обнаруженных для отслеживания", "make_sure_browser_is_properly_installed": "Убедитесь, что {браузер} правильно установлен"}, "auth": {"title": "Менеджер авторизации Cursor", "checking_auth": "Проверка файла авторизации", "auth_not_found": "Файл авторизации не найден", "auth_file_error": "Ошибка файла авторизации: {error}", "reading_auth": "Чтение файла авторизации", "updating_auth": "Обновление информации авторизации", "auth_updated": "Информация авторизации успешно обновлена", "auth_update_failed": "Ошибка обновления информации авторизации: {error}", "auth_file_created": "Файл авторизации создан", "auth_file_create_failed": "Ошибка создания файла авторизации: {error}", "press_enter": "Нажмите Enter для выхода", "reset_machine_id": "Сброс ID машины", "database_connection_closed": "Соединение с базой данных закрыто", "database_updated_successfully": "База данных успешно обновлена", "connected_to_database": "Подключено к базе данных", "updating_pair": "Обновление пары ключ-значение", "db_not_found": "Файл базы данных не найден по пути: {path}", "db_permission_error": "Невозможно получить доступ к файлу базы данных. Проверьте права доступа", "db_connection_error": "Ошибка подключения к базе данных: {error}"}, "control": {"generate_email": "Генерация нового email", "blocked_domain": "Заблокированный домен", "select_domain": "Выбор случайного домена", "copy_email": "Копирование email адреса", "enter_mailbox": "Вход в почтовый ящик", "refresh_mailbox": "Обновление почтового ящика", "check_verification": "Проверка кода подтверждения", "verification_found": "Код подтверждения найден", "verification_not_found": "Код подтверждения не найден", "browser_error": "Ошибка управления браузером: {error}", "navigation_error": "Ошибка навигации: {error}", "email_copy_error": "Ошибка копирования email: {error}", "mailbox_error": "Ошибка почтового ящика: {error}", "token_saved_to_file": "Токен сохранен в cursor_tokens.txt", "navigate_to": "Переход на {url}", "generate_email_success": "Email успешно сгенерирован", "select_email_domain": "Выбор домена email", "select_email_domain_success": "Домен email успешно выбран", "get_email_name": "Получение имени email", "get_email_name_success": "Имя email успешно получено", "get_email_address": "Получение email адреса", "get_email_address_success": "Email адрес успешно получен", "enter_mailbox_success": "Успешный вход в почтовый ящик", "found_verification_code": "Найден код подтверждения", "get_cursor_session_token": "Получение токена сессии Cursor", "get_cursor_session_token_success": "Токен сессии Cursor успешно получен", "get_cursor_session_token_failed": "Ошибка получения токена сессии Cursor", "save_token_failed": "Ошибка сохранения токена", "database_updated_successfully": "База данных успешно обновлена", "database_connection_closed": "Соединение с базой данных закрыто", "no_valid_verification_code": "Нет действительного кода подтверждения"}, "email": {"starting_browser": "Запуск браузера", "visiting_site": "Переход на сайты почтовых доменов", "create_success": "Email успешно создан", "create_failed": "Не удалось создать email", "create_error": "Ошибка создания email: {error}", "refreshing": "Обновление email", "refresh_success": "Email успешно обновлен", "refresh_error": "Ошибка обновления email: {error}", "refresh_button_not_found": "Кнопка обновления не найдена", "verification_found": "Проверка найдена", "verification_not_found": "Проверка не найдена", "verification_error": "Ошибка проверки: {error}", "verification_code_found": "Код подтверждения найден", "verification_code_not_found": "Код подтверждения не найден", "verification_code_error": "Ошибка кода подтверждения: {error}", "address": "<PERSON><PERSON> адре<PERSON>", "all_domains_blocked": "Все домены заблокированы, переключение сервиса", "no_available_domains_after_filtering": "Нет доступных доменов после фильтрации", "switching_service": "Переключение на сервис {service}", "domains_list_error": "Не удалось получить список доменов: {error}", "failed_to_get_available_domains": "Не удалось получить доступные домены", "domains_excluded": "Исключенные домены: {domains}", "failed_to_create_account": "Не удалось создать аккаунт", "account_creation_error": "Ошибка создания аккаунта: {error}", "blocked_domains": "Заблокированные домены: {domains}", "blocked_domains_loaded": "Загружены заблокированные домены: {count}", "blocked_domains_loaded_error": "Ошибка загрузки заблокированных доменов: {error}", "blocked_domains_loaded_success": "Заблокированные домены успешно загружены", "blocked_domains_loaded_timeout": "Таймаут загрузки заблокированных доменов: {timeout}с", "blocked_domains_loaded_timeout_error": "Ошибка таймаута загрузки заблокированных доменов: {error}", "available_domains_loaded": "Загружены доступные домены: {count}", "domains_filtered": "Отфильтрованы домены: {count}", "trying_to_create_email": "Попытка создания email: {email}", "domain_blocked": "Домен заблокирован: {domain}", "no_display_found": "Не найдено дисплея. Убедитесь, что X Server работает.", "try_export_display": "Попробуйте: экспорт Display =: 0", "try_install_chromium": "Попробуйте: Sudo Apt Установите Chromium-Browser", "extension_load_error": "Ошибка загрузки расширения: {ошибка}", "make_sure_chrome_chromium_is_properly_installed": "Убедитесь, что Chrome/Chromium правильно установлен", "using_chrome_profile": "Использование Chrome Profile от: {user_data_dir}"}, "update": {"title": "Отключение автоматического обновления Cursor", "disable_success": "Автоматическое обновление успешно отключено", "disable_failed": "Ошибка отключения автоматического обновления: {error}", "press_enter": "Нажмите Enter для выхода", "start_disable": "Начало отключения автоматического обновления", "killing_processes": "Завершение процессов", "processes_killed": "Процессы завершены", "removing_directory": "Удаление директории", "directory_removed": "Директория удалена", "creating_block_file": "Создание файла блокировки", "block_file_created": "Файл блокировки создан", "clearing_update_yml": "Очистка update.yml файл", "update_yml_cleared": "update.yml файл очищен", "unsupported_os": "Неподдерживаемая ОС: {Система}", "block_file_already_locked": "Блок -файл уже заблокирован", "yml_already_locked_error": "update.yml файл уже заблокированная ошибка: {error}", "update_yml_not_found": "update.yml файл не найден", "yml_locked_error": "update.yml -файл заблокирован ошибка: {error}", "remove_directory_failed": "Не удалось удалить каталог: {ошибка}", "yml_already_locked": "file update.yml уже заблокирован", "create_block_file_failed": "Не удалось создать файл блока: {ошибка}", "block_file_locked_error": "Блок -файл заблокирован ошибка: {ошибка}", "directory_locked": "Ката<PERSON>ог заблокирован: {path}", "block_file_already_locked_error": "Блок -файл уже заблокированная ошибка: {ошибка}", "clear_update_yml_failed": "Не удалось очистить файл update.yml: {error}", "yml_locked": "file update.yml заблокирован", "block_file_locked": "Заблокированный файл блока"}, "updater": {"checking": "Проверка обновлений...", "new_version_available": "Доступна новая версия! (Текущая: {current}, Последняя: {latest})", "updating": "Обновление до последней версии. Программа перезапустится автоматически.", "up_to_date": "У вас установлена последняя версия.", "check_failed": "Не удалось проверить обновления: {error}", "continue_anyway": "Продолжение работы с текущей версией...", "update_confirm": "Хотите обновить до последней версии? (Y/n)", "update_skipped": "Обновление пропущено.", "invalid_choice": "Неверный выбор. Пожалуйста, введите 'Y' или 'n'.", "development_version": "Версия разработки {current} > {latest}", "changelog_title": "Список изменений", "rate_limit_exceeded": "Предел ставки GitHub API превышен. Пропустить проверку обновления."}, "totally_reset": {"title": "Полный сброс Cursor", "checking_config": "Проверка конфигурационного файла", "config_not_found": "Конфигурационный файл не найден", "no_permission": "Невозможно прочитать или записать конфигурационный файл, проверьте права доступа", "reading_config": "Чтение текущей конфигурации", "creating_backup": "Создание резервной копии конфигурации", "backup_exists": "Резервный файл уже существует, пропускаем шаг резервного копирования", "generating_new_machine_id": "Генерация нового ID машины", "saving_new_config": "Сохранение новой конфигурации в JSON", "success": "Cursor успешно сброшен", "error": "Ошибка сброса Cursor: {error}", "press_enter": "Нажмите Enter для выхода", "reset_machine_id": "Сброс ID машины", "database_connection_closed": "Соединение с базой данных закрыто", "database_updated_successfully": "База данных успешно обновлена", "connected_to_database": "Подключено к базе данных", "updating_pair": "Обновление пары ключ-значение", "db_not_found": "Файл базы данных не найден по пути: {path}", "db_permission_error": "Невозможно получить доступ к файлу базы данных. Проверьте права доступа", "db_connection_error": "Ошибка подключения к базе данных: {error}", "feature_title": "ФУНКЦИИ", "feature_1": "Полное удаление настроек и конфигураций Cursor AI", "feature_2": "Очистка всех кэшированных данных, включая историю AI и промпты", "feature_3": "Сброс ID машины для обхода обнаружения пробной версии", "feature_4": "Создание новых случайных идентификаторов машины", "feature_5": "Удаление пользовательских расширений и настроек", "feature_6": "Сброс информации о пробной версии и данных активации", "feature_7": "Глубокий поиск скрытых файлов лицензии и пробной версии", "feature_8": "Безопасное сохранение файлов и приложений, не относящихся к Cursor", "feature_9": "Совместимость с Windows, macOS и Linux", "disclaimer_title": "ПРЕДУПРЕЖДЕНИЕ", "disclaimer_1": "Этот инструмент навсегда удалит все настройки Cursor AI,", "disclaimer_2": "конфигурации и кэшированные данные. Это действие нельзя отменить.", "disclaimer_3": "Ваши файлы кода НЕ будут затронуты, и инструмент разработан", "disclaimer_4": "только для файлов редактора Cursor AI и механизмов обнаружения пробной версии.", "disclaimer_5": "Другие приложения на вашей системе не будут затронуты.", "disclaimer_6": "После запуска этого инструмента вам нужно будет настроить Cursor AI заново.", "disclaimer_7": "Используйте на свой страх и риск", "confirm_title": "Вы уверены, что хотите продолжить?", "confirm_1": "Это действие удалит все настройки Cursor AI,", "confirm_2": "конфигурации и кэшированные данные. Это действие нельзя отменить.", "confirm_3": "Ваши файлы кода НЕ будут затронуты, и инструмент разработан", "confirm_4": "только для файлов редактора Cursor AI и механизмов обнаружения пробной версии.", "confirm_5": "Другие приложения на вашей системе не будут затронуты.", "confirm_6": "После запуска этого инструмента вам нужно будет настроить Cursor AI заново.", "confirm_7": "Используйте на свой страх и риск", "invalid_choice": "Пожалуйста, введите 'Y' или 'n'", "skipped_for_safety": "Пропущено для безопасности (не относится к Cursor): {path}", "deleted": "Удалено: {path}", "error_deleting": "Ошибка удаления {path}: {error}", "not_found": "Файл не найден: {path}", "resetting_machine_id": "Сброс идентификаторов машины для обхода обнаружения пробной версии...", "created_machine_id": "Создан новый ID машины: {path}", "error_creating_machine_id": "Ошибка создания файла ID машины {path}: {error}", "error_searching": "Ошибка поиска файлов в {path}: {error}", "created_extended_trial_info": "Создана новая расширенная информация о пробной версии: {path}", "error_creating_trial_info": "Ошибка создания файла информации о пробной версии {path}: {error}", "resetting_cursor_ai_editor": "Сброс редактора Cursor AI... Пожалуйста, подождите.", "reset_cancelled": "Сброс отменен. Выход без внесения изменений.", "windows_machine_id_modification_skipped": "Изменение ID машины Windows пропущено: {error}", "linux_machine_id_modification_skipped": "Изменение machine-id Linux пропущено: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Примечание: полный сброс ID машины может потребовать запуска от имени администратора", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Примечание: полный сброс системного machine-id может потребовать прав sudo", "windows_registry_instructions": "📝 ПРИМЕЧАНИЕ: Для полного сброса в Windows может потребоваться очистка записей реестра.", "windows_registry_instructions_2": "   Запустите 'regedit' и найдите ключи, содержащие 'Cursor' или 'CursorAI' в HKEY_CURRENT_USER\\Software\\ и удалите их.\n", "reset_log_1": "Cursor AI полностью сброшен и обнаружение пробной версии обойдено!", "reset_log_2": "Пожалуйста, перезагрузите систему для применения изменений.", "reset_log_3": "Вам нужно будет переустановить Cursor AI, и теперь у вас должен быть новый пробный период.", "reset_log_4": "Для лучших результатов рекомендуется также:", "reset_log_5": "Использовать другой email адрес при регистрации нового пробного периода", "reset_log_6": "Если возможно, использовать VPN для изменения IP адреса", "reset_log_7": "Очистить куки и кэш браузера перед посещением сайта Cursor AI", "reset_log_8": "Если проблемы сохраняются, попробуйте установить Cursor AI в другое место", "reset_log_9": "Если вы столкнулись с проблемами, перейдите на Github Issue Tracker и создайте issue на https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Произошла непредвиденная ошибка: {error}", "report_issue": "Пожалуйста, сообщите об этой проблеме на Github Issue Tracker на https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "Процесс прерван пользователем. Выход...", "return_to_main_menu": "Возврат в главное меню...", "process_interrupted": "Процесс прерван. Выход...", "press_enter_to_return_to_main_menu": "Нажмите Enter для возврата в главное меню...", "removing_known": "Удаление известных файлов лицензии/пробной версии", "performing_deep_scan": "Выполнение глубокого поиска дополнительных файлов лицензии/пробной версии", "found_additional_potential_license_trial_files": "Найдено {count} дополнительных потенциальных файлов лицензии/пробной версии", "checking_for_electron_localstorage_files": "Проверка файлов localStorage Electron", "no_additional_license_trial_files_found_in_deep_scan": "Дополнительные файлы лицензии/пробной версии не найдены при глубоком поиске", "removing_electron_localstorage_files": "Удаление файлов localStorage Electron", "electron_localstorage_files_removed": "Файлы localStorage Electron удалены", "electron_localstorage_files_removal_error": "Ошибка удаления файлов localStorage Electron: {error}", "removing_electron_localstorage_files_completed": "Удаление файлов localStorage Electron завершено", "warning_title": "ПРЕДУПРЕЖДЕНИЕ", "direct_advanced_navigation": "Попытка прямой навигации к вкладке Advanced", "delete_input_error": "Ошибка поиска Удаления ввода: {ошибка}", "delete_input_not_found_continuing": "Удалить ввод подтверждения не найден, пытаясь продолжить в любом случае", "advanced_tab_not_found": "Вкладка Advanced не найдена после нескольких попыток", "advanced_tab_error": "Вкладка «Объединение ошибок»: {error}", "delete_input_not_found": "Удалить вход подтверждения не найден после нескольких попыток", "failed_to_delete_file": "Не удалось удалить файл: {path}", "operation_cancelled": "Операция отменена. Выйдя без каких -либо изменений.", "removed": "Удалено: {path}", "warning_6": "Вам нужно будет снова настроить AI Cursor AI после запуска этого инструмента.", "delete_input_retry": "Удалить вход не найден, попытка {попытка}/{max_attempts}", "warning_4": "Нацеливаться только на файлы редактора Cursor AI и механизмы обнаружения испытаний.", "cursor_reset_failed": "Cursor AI Редактор REDITOR RESET не удалось: {ошибка}", "login_redirect_failed": "Перенаправление входа в систему не удалось, пробуя прямую навигацию ...", "warning_5": "Другие приложения в вашей системе не будут затронуты.", "failed_to_delete_file_or_directory": "Не удалось удалить файл или каталог: {path}", "failed_to_delete_directory": "Не удалось удалить каталог: {path}", "resetting_cursor": "Сброс редактора Cursor AI ... Пожалуйста, подождите.", "cursor_reset_completed": "Курсор AI Редактор был полностью сброшен, и обнаружение испытаний обходится!", "warning_3": "Ваши кодовые файлы не будут затронуты, а инструмент разработан", "advanced_tab_retry": "Вкладка Advanced не найдена, попытка {попытка}/{max_attempts}", "completed_in": "Завершено в {время} секунд", "advanced_tab_clicked": "Нажал на вкладку «Дополнительно", "already_on_settings": "Уже на странице настроек", "delete_button_retry": "Кнопка удаления не найдена, попытка {попытка}/{max_attempts}", "found_danger_zone": "Нашел раздел зоны опасности", "failed_to_remove": "Не удалось удалить: {path}", "failed_to_reset_machine_guid": "Не удалось сбросить машину", "deep_scanning": "Выполнение глубокого сканирования для дополнительных судебных/лицензионных файлов", "delete_button_clicked": "Нажал кнопку «Удалить учетную запись»", "warning_7": "Используйте свой собственный риск", "delete_button_not_found": "Удалить кнопку учетной записи не найдена после нескольких попыток", "delete_button_error": "Кнопка «Удалить ошибку»: {ошибка}", "warning_2": "конфигурации и кэшированные данные. Это действие не может быть отменено.", "warning_1": "Это действие удалит все настройки AI курсора,", "navigating_to_settings": "Навигация на страницу настроек ...", "cursor_reset_cancelled": "Курсор AI Редактор сброс отменен. Выйдя без каких -либо изменений."}, "chrome_profile": {"title": "Выбор Профиля Chrome", "select_profile": "Выберите профиль Chrome для использования:", "profile_list": "Доступные профили:", "default_profile": "Профиль по умолчанию", "profile": "Профиль {number}", "no_profiles": "Профили Chrome не найдены", "error_loading": "Ошибка загрузки профилей Chrome: {error}", "profile_selected": "Выбран профиль: {profile}", "invalid_selection": "Неверный выбор. Пожалуйста, попробуйте снова", "warning_chrome_close": "Предупреждение: Это закроет все запущенные процессы Chrome"}, "restore": {"title": "Восстановить ID устройства из резервной копии", "starting": "Запуск процесса восстановления ID устройства", "no_backups_found": "Резервные копии не найдены", "available_backups": "Доступные резервные копии", "select_backup": "Выберите резервную копию для восстановления", "to_cancel": "для отмены", "operation_cancelled": "Операция отменена", "invalid_selection": "Неверный выбор", "please_enter_number": "Пожалуйста, введите корректный номер", "missing_id": "Отсутствует ID: {id}", "read_backup_failed": "Не удалось прочитать файл резервной копии: {error}", "current_file_not_found": "Текущий файл хранилища не найден", "current_backup_created": "Создана резервная копия текущего файла хранилища", "storage_updated": "Файл хранилища успешно обновлен", "update_failed": "Не удалось обновить файл хранилища: {error}", "sqlite_not_found": "База данных SQLite не найдена", "updating_sqlite": "Обновление базы данных SQLite", "updating_pair": "Обновление пары ключ-значение", "sqlite_updated": "База данных SQLite успешно обновлена", "sqlite_update_failed": "Не удалось обновить базу данных SQLite: {error}", "machine_id_backup_created": "Создана резервная копия файла machineId", "backup_creation_failed": "Не удалось создать резервную копию: {error}", "machine_id_updated": "Файл machineId успешно обновлен", "machine_id_update_failed": "Не удалось обновить файл machineId: {error}", "updating_system_ids": "Обновление системных ID", "system_ids_update_failed": "Не удалось обновить системные ID: {error}", "permission_denied": "Доступ запрещен. Попробуйте запустить с правами администратора", "windows_machine_guid_updated": "GUID устройства Windows успешно обновлен", "update_windows_machine_guid_failed": "Не удалось обновить GUID устройства Windows: {error}", "windows_machine_id_updated": "ID устройства Windows успешно обновлен", "update_windows_machine_id_failed": "Не удалось обновить ID устройства Windows: {error}", "sqm_client_key_not_found": "Ключ реестра SQMClient не найден", "update_windows_system_ids_failed": "Не удалось обновить системные ID Windows: {error}", "macos_platform_uuid_updated": "UUID платформы macOS успешно обновлен", "failed_to_execute_plutil_command": "Не удалось выполнить команду plutil", "update_macos_system_ids_failed": "Не удалось обновить системные ID macOS: {error}", "ids_to_restore": "ID устройства для восстановления", "confirm": "Вы уверены, что хотите восстановить эти ID?", "success": "ID устройства успешно восстановлен", "process_error": "Ошибка процесса восстановления: {error}", "press_enter": "Нажмите Enter для продолжения"}, "oauth": {"no_chrome_profiles_found": "Профили хрома не обнаружены, используя по умолчанию", "starting_new_authentication_process": "Запуск нового процесса аутентификации ...", "failed_to_delete_account": "Не удалось удалить учетную запись: {ошибка}", "found_email": "Найдено электронное письмо: {Электронная почта}", "github_start": "GitHub Start", "already_on_settings_page": "Уже на странице настроек!", "starting_github_authentication": "Начальная аутентификация GitHub ...", "account_is_still_valid": "Учетная запись все еще действительна (использование: {использование})", "status_check_error": "Ошибка проверки состояния: {ошибка}", "authentication_timeout": "Тайм -аут аутентификации", "usage_count": "Количество использования: {использование}", "using_first_available_chrome_profile": "Использование первого доступного Chrome Profile: {профиль}", "google_start": "Google Start", "no_compatible_browser_found": "Совместимый браузер не найден. Пожалуйста, установите Google Chrome или Chromium.", "authentication_successful_getting_account_info": "Успешная аутентификация, получение информации об учетной записи ...", "found_chrome_at": "Нашел хром в: {path}", "error_getting_user_data_directory": "Ошибка", "error_finding_chrome_profile": "Ошибка поиска хрома, используя по умолчанию: {error}", "auth_update_success": "Успех обновления автоза", "authentication_successful": "Успешная аутентификация - электронная почта: {электронная почта}", "authentication_failed": "Аутентификация не удалась: {ошибка}", "warning_browser_close": "Предупреждение: это закроет все запуск {браузер} процессов", "supported_browsers": "Поддерживаемые браузеры для {платформы}", "authentication_button_not_found": "Кнопка аутентификации не найдена", "starting_new_google_authentication": "Начало новой аутентификации Google ...", "waiting_for_authentication": "В ожидании аутентификации ...", "found_default_chrome_profile": "Найденный хромированный профиль по умолчанию", "starting_browser": "Начальный браузер на: {path}", "token_extraction_error": "Ошибка извлечения токена: {ошибка}", "could_not_check_usage_count": "Не удалось проверить количество использования: {ошибка}", "profile_selection_error": "Ошибка во время выбора профиля: {ошибка}", "warning_could_not_kill_existing_browser_processes": "Предупреждение: не удалось убить существующие процессы браузера: {ошибка}", "browser_failed_to_start": "Браузер не смог запустить: {ошибка}", "redirecting_to_authenticator_cursor_sh": "Передача на Authenticator.cursor.sh ...", "starting_re_authentication_process": "Начало процесса повторной аутентификации ...", "found_browser_data_directory": "Нашел каталог данных браузера: {path}", "browser_not_found_trying_chrome": "Не удалось найти {браузер}, попробовать хром вместо этого", "found_cookies": "Найдено {count} cookie", "auth_update_failed": "Обновление ауты не удалось", "browser_failed_to_start_fallback": "Браузер не смог запустить: {ошибка}", "failed_to_delete_expired_account": "Не удалось удалить учетную запись с истекшим сроком действия", "navigating_to_authentication_page": "Навигация на страницу аутентификации ...", "initializing_browser_setup": "Инициализация настройки браузера ...", "browser_closed": "Брау<PERSON>е<PERSON> закрыт", "detected_platform": "Обнаруженная платформа: {платформа}", "failed_to_delete_account_or_re_authenticate": "Не удалось удалить учетную запись или повторную аутентификацию: {ошибка}", "failed_to_extract_auth_info": "Не удалось извлечь auth info: {error}", "starting_google_authentication": "Начало аутентификации Google ...", "using_browser_profile": "Использование профиля браузера: {профиль}", "browser_failed": "Браузер не смог запустить: {ошибка}", "consider_running_without_sudo": "Подумайте о запуске сценария без SUDO", "try_running_without_sudo_admin": "Попробуйте запустить без привилегий Sudo/Administrator", "page_changed_checking_auth": "Страница изменилась, проверяю аут ...", "running_as_root_warning": "Запуск как root не рекомендуется для автоматизации браузера", "please_select_your_google_account_to_continue": "Пожалуйста, выберите свою учетную запись Google, чтобы продолжить ...", "browser_setup_failed": "Недостаточная настройка браузера: {ошибка}", "missing_authentication_data": "Отсутствие данных аутентификации: {data}", "using_configured_browser_path": "Использование Configured {Browser} Path: {path}", "killing_browser_processes": "Убийство {браузер} процессы ...", "could_not_find_usage_count": "Не удалось найти количество использования: {ошибка}", "browser_setup_completed": "Настройка браузера завершена успешно", "account_has_reached_maximum_usage": "Учетная запись достигла максимального использования, {удаление}", "could_not_find_email": "Не удалось найти электронную почту: {ошибка}", "found_browser_user_data_dir": "Нашел {браузер} каталог данных пользователей: {path}", "user_data_dir_not_found": "{браузер} каталог пользовательских данных не найден в {path}, вместо этого попробую Chrome", "invalid_authentication_type": "Неверный тип аутентификации"}, "auth_check": {"token_length": "Длина токена: {длина} символы", "usage_response_status": "Статус ответа на использование: {ответ}", "operation_cancelled": "Операция отменена пользователем", "error_getting_token_from_db": "Ошибка получения токена из базы данных: {ошибка}", "checking_usage_information": "Проверка информации об использовании ...", "usage_response": "Ответ об использовании: {ответ}", "authorization_failed": "Авторизация не удалась!", "authorization_successful": "Авторизация успешно!", "request_timeout": "Запросить время", "check_error": "Проверка ошибок Авторизация: {ошибка}", "connection_error": "Ошибка соединения", "invalid_token": "Неверный токен", "check_usage_response": "Проверьте ответ на использование: {ответ}", "enter_token": "Введите токен курсора:", "token_found_in_db": "То<PERSON><PERSON><PERSON>, найденный в базе данных", "user_unauthorized": "Пользователь несанкционирован", "checking_authorization": "Проверка авторизации ...", "error_generating_checksum": "Контрольная сумма с генерированием ошибок: {ошибка}", "token_source": "Получить токен из базы данных или ввода вручную? (D/M, по умолчанию: D)", "unexpected_error": "Неожиданная ошибка: {ошибка}", "user_authorized": "Пользователь авторизован", "token_not_found_in_db": "Токен не найден в базе данных", "unexpected_status_code": "Неожиданный код статуса: {код}", "jwt_token_warning": "Токен, по -видимому, находится в формате JWT, но проверка API вернула неожиданный код состояния. Токен может быть действительным, но доступ к API ограничен.", "getting_token_from_db": "Получение значения из базы данных ...", "cursor_acc_info_not_found": "CURSOR_ACC_INFO.PY не найден"}, "manual_auth": {"auth_type_selected": "Выбранный тип аутентификации: {type}", "proceed_prompt": "Продолжить? (Y/N):", "auth_type_github": "GitHub", "confirm_prompt": "Пожалуйста, подтвердите следующую информацию:", "invalid_token": "Неверный токен. Аутентификация прервана.", "continue_anyway": "В любом случае продолжить? (Y/N):", "token_verified": "Токен проверил успешно!", "error": "Ошибка: {ошибка}", "auth_update_failed": "Не удалось обновить информацию о аутентификации", "auth_type_auth0": "Auth_0 (по умолчанию)", "auth_type_prompt": "Выберите Ти<PERSON> аутентификации:", "verifying_token": "Проверка достоверности токена ...", "auth_updated_successfully": "Информация об аутентификации успешно обновлена!", "email_prompt": "Введите электронное письмо (оставьте пусто для случайной электронной почты):", "token_prompt": "Введите токен курсора (access_token/represh_token):", "title": "Ручная аутентификация курсора", "random_email_generated": "Сгенерировано случайная электронная почта: {электронная почта}", "token_verification_skipped": "Проверка токена пропустила (check_user_authorized.py не найдена)", "token_required": "Токен требуется", "auth_type_google": "Google", "operation_cancelled": "Операция отменена", "token_verification_error": "Проверка ошибки токен: {ошибка}", "updating_database": "Обновление базы данных аутентификации курсора ..."}, "account_delete": {"delete_input_not_found": "Удалить вход подтверждения не найден после нескольких попыток", "logging_in": "Вход в систему с Google ...", "confirm_button_not_found": "Подтвердить кнопку не найдена после нескольких попыток", "confirm_button_error": "Кнопка «Подтверждение ошибки»: {ошибка}", "delete_button_clicked": "Нажал кнопку «Удалить учетную запись»", "confirm_prompt": "Вы уверены, что хотите продолжить? (Y/N):", "cancelled": "Удаление учетной записи отменено.", "delete_button_error": "Кнопка «Удалить ошибку»: {ошибка}", "interrupted": "Процесс удаления учетной записи прерван пользователем.", "error": "Ошибка во время удаления учетной записи: {ошибка}", "delete_input_not_found_continuing": "Удалить ввод подтверждения не найден, пытаясь продолжить в любом случае", "advanced_tab_retry": "Вкладка Advanced не найдена, попытка {попытка}/{max_attempts}", "waiting_for_auth": "В ожидании аутентификации Google ...", "typed_delete": "Напечатано «Удалить» в поле подтверждения", "trying_settings": "Попытка перейти на страницу настроек ...", "delete_input_retry": "Удалить вход не найден, попытка {попытка}/{max_attempts}", "email_not_found": "Электронная почта не найдена: {ошибка}", "delete_button_not_found": "Удалить кнопку учетной записи не найдена после нескольких попыток", "already_on_settings": "Уже на странице настроек", "failed": "Процесс удаления учетной записи не удался или был отменен.", "warning": "Предупреждение: это навсегда удалит вашу учетную запись курсора. Это действие не может быть отменено.", "direct_advanced_navigation": "Попытка прямой навигации к вкладке Advanced", "advanced_tab_not_found": "Вкладка Advanced не найдена после нескольких попыток", "auth_timeout": "Тайм -аут аутентификации, все равно продолжая ...", "select_google_account": "Пожалуйста, выберите свою учетную запись Google ...", "google_button_not_found": "Кнопка входа в систему Google не найдена", "found_danger_zone": "Нашел раздел зоны опасности", "account_deleted": "Учебный счет удален успешно!", "advanced_tab_error": "Вкладка «Объединение ошибок»: {error}", "starting_process": "Начальный процесс удаления учетной записи ...", "delete_button_retry": "Кнопка удаления не найдена, попытка {попытка}/{max_attempts}", "login_redirect_failed": "Перенаправление входа в систему не удалось, пробуя прямую навигацию ...", "unexpected_error": "Неожиданная ошибка: {ошибка}", "login_successful": "Вход успешно", "delete_input_error": "Ошибка поиска Удаления ввода: {ошибка}", "advanced_tab_clicked": "Нажал на вкладку «Дополнительно", "unexpected_page": "Неожиданная страница после входа в систему: {url}", "found_email": "Найдено электронное письмо: {Электронная почта}", "title": "Кур<PERSON><PERSON><PERSON> Google инструмент удаления учетной записи", "navigating_to_settings": "Навигация на страницу настроек ...", "success": "Ваша учетная запись курсора была успешно удалена!", "confirm_button_retry": "Кнопка подтверждения не найдена, попытка {попытка}/{max_attempts}"}, "token": {"refreshing": "Освежающий токен ...", "extraction_error": "Ошибка извлечения токена: {ошибка}", "invalid_response": "Неверный ответ JSON с сервера обновления", "no_access_token": "Нет токена доступа в ответ", "connection_error": "Ошибка соединения, чтобы обновить сервер", "unexpected_error": "Неожиданная ошибка во время обновления токена: {ошибка}", "server_error": "Ошибка обновления сервера: http {status}", "refresh_success": "Токен успешно обновлен! Действительно для {дней} дней (истекает: {истекает})", "request_timeout": "Запрос на обновления сервера", "refresh_failed": "Производительное обновление токена: {ошибка}"}, "browser_profile": {"profile_selected": "Выбранный профиль: {профиль}", "default_profile": "Профиль по умолчанию", "no_profiles": "Нет {браузер} профили найдены", "select_profile": "Выберите {браузер} Профиль для использования:", "error_loading": "Ошибка загрузки {браузер} профили: {ошибка}", "invalid_selection": "Неверный выбор. Пожалуйста, попробуйте еще раз.", "title": "Выбор профиля браузера", "profile": "Профиль {номер}", "profile_list": "Доступны профили {браузер}:"}, "github_register": {"feature2": "Регистрирует новую учетную запись GitHub со случайными учетными данными.", "feature6": "Сохраняет все учетные данные в файл.", "starting_automation": "Начальная автоматизация ...", "feature1": "Генерирует временное электронное письмо с помощью 1Secmail.", "title": "GitHub + Cursor AI Автоматизация регистрации", "github_username": "GitHub username", "check_browser_windows_for_manual_intervention_or_try_again_later": "Проверьте окна браузера для ручного вмешательства или попробуйте еще раз позже.", "warning1": "Этот скрипт автоматизирует создание учетной записи, что может нарушать условия обслуживания GitHub/Cursor.", "feature4": "Войдет в AI Cursor AI, используя аутентификацию GitHub.", "completed_successfully": "Gith<PERSON> + cursor Регистрация завершена успешно!", "invalid_choice": "Неверный выбор. Пожалуйста, введите «да» или «нет»", "warning2": "Требуется доступ в Интернет и административные привилегии.", "registration_encountered_issues": "GitHub + Регистрация курсора столкнулась с проблемами.", "credentials_saved": "Эти учетные данные были сохранены на github_cursor_accounts.txt", "feature3": "Проверяет электронную почту GitHub автоматически.", "github_password": "GitHub пароль", "features_header": "Функции", "feature5": "Сбрасывает идентификатор машины, чтобы обойти пробное обнаружение.", "warning4": "Используйте ответственно и на ваш собственный риск.", "warning3": "CAPTCHA или дополнительная проверка может прервать автоматизацию.", "cancelled": "Операция отменена", "warnings_header": "Предупреждения", "program_terminated": "Программа завершена пользователем", "confirm": "Вы уверены, что хотите продолжить?", "email_address": "Адрес электронной почты"}, "account_info": {"subscription": "Подписка", "failed_to_get_account_info": "Не удалось получить информацию об учетной записи", "subscription_type": "Тип подписки", "pro": "Профиль", "failed_to_get_account": "Не удалось получить информацию об учетной записи", "config_not_found": "Конфигурация не найдена.", "premium_usage": "Использование премиум -класса", "failed_to_get_subscription": "Не удалось получить информацию о подписке", "basic_usage": "Основное использование", "premium": "Премиум", "free": "Бесплатно", "email_not_found": "Электронная почта не найдена", "title": "Информация об учетной записи", "remaining_trial": "Оставшееся испытание", "inactive": "Неактивный", "enterprise": "Предприятие", "failed_to_get_usage": "Не удалось получить информацию об использовании", "usage_not_found": "Использование не найдено", "lifetime_access_enabled": "Доступ к жизни включен", "days_remaining": "Дни остаются", "failed_to_get_token": "Не удалось получить токен", "token": "Токен", "subscription_not_found": "Информация о подписке не найдена", "days": "дни", "team": "Команда", "token_not_found": "Токен не найден", "active": "Активный", "email": "Электронная почта", "pro_trial": "PRO TREAD", "failed_to_get_email": "Не удалось получить адрес электронной почты", "trial_remaining": "Оставшиеся профессиональное испытание", "usage": "Использование"}, "config": {"configuration": "Конфигурация", "config_updated": "Конфигурация обновлена", "file_owner": "Владелец файла: {владелец}", "error_checking_linux_paths": "Проверка ошибок путей Linux: {ошибка}", "storage_file_is_empty": "Файл хранения пуст: {storage_path}", "config_directory": "Справочник конфигурации", "config_not_available": "Конфигурация недоступна", "documents_path_not_found": "Документы не найдены, используя текущий каталог", "neither_cursor_nor_cursor_directory_found": "Ни курсор, ни курсора, найденный в {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Пожалуйста, убедите<PERSON><PERSON>, что курсор установлен и запускается хотя бы один раз", "config_created": "Конфигурация создана: {config_file}", "using_temp_dir": "Использование временного каталога из -за ошибки: {path} (error: {error})", "storage_file_not_found": "Файл хранения не найден: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "Файл может быть поврежден, пожалуйста, переустановите курсор", "error_getting_file_stats": "Ошибка Получение статистики файла: {ошибка}", "enabled": "Включено", "backup_created": "Резервное копирование создано: {path}", "file_permissions": "Разрешения на файл: {разрешения}", "config_setup_error": "Ошибка настройки config: {error}", "config_force_update_enabled": "Включено обновление силы файла конфигурации, выполнение принудительного обновления", "config_removed": "Файл конфигурации удален для принудительного обновления", "file_size": "Размер файла: {размер} байты", "error_reading_storage_file": "Файл хранения ошибок: {ошибка}", "config_force_update_disabled": "Обновление файла файла конфигурации отключено, пропуская принудительное обновление", "config_dir_created": "Справочник конфигурации создан: {path}", "config_option_added": "Вариант конфигурации добавлена: {опция}", "file_group": "Группа файлов: {группа}", "and": "И", "backup_failed": "Не удалось сделать резервное копирование config: {error}", "force_update_failed": "Не удалось конфигурация обновления силы: {ошибка}", "storage_directory_not_found": "Каталог хранилища не найден: {storam_dir}", "also_checked": "Также проверено {path}", "try_running": "Попробуйте запустить: {Команда}", "disabled": "Неполноценный", "storage_file_found": "Найден файл хранилища: {storage_path}", "storage_file_is_valid_and_contains_data": "Файл хранения действителен и содержит данные", "permission_denied": "Разрешение отказано: {storage_path}"}, "bypass": {"found_product_json": "Нашел продукт.json: {path}", "starting": "Запуск курсора обход версии ...", "version_updated": "Версия обновлена ​​от {old} до {new}", "menu_option": "Проверка версии курсора обходного курса", "unsupported_os": "Неподдерживаемая операционная система: {System}", "backup_created": "Резервное копирование создано: {path}", "current_version": "Текущая версия: {версия}", "no_write_permission": "Нет разрешения на запись для файла: {path}", "localappdata_not_found": "Локальная переменная среды не найдена", "write_failed": "Не удалось написать product.json: {error}", "description": "Этот инструмент изменяет Cursor's Product.json, чтобы обходить ограничения версий", "bypass_failed": "Ошибка обхода версии: {ошибка}", "title": "Инструмент обхода версии курсора", "no_update_needed": "Обновление не требуется. Текущая версия {версия} уже> = 0,46,0", "read_failed": "Не удалось прочитать product.json: {error}", "stack_trace": "Стек трассировки", "product_json_not_found": "Product.json не найден в обычных путях Linux", "file_not_found": "Файл не найден: {path}"}, "bypass_token_limit": {"description": "Этот инструмент изменяет файл workbench.desktop.main.js, чтобы обойти предел токена", "press_enter": "Нажмите En<PERSON>, чтобы продолжить ...", "title": "Инструмент ограничения обхода токена"}, "tempmail": {"no_email": "Электронное письмо с проверкой курсора не найдено", "config_error": "Ошибка файла конфигурации: {ошибка}", "extract_code_failed": "Установка кода извлечения проверки: {ошибка}", "general_error": "Произошла ошибка: {ошибка}", "no_code": "Не удалось получить код проверки", "checking_email": "Проверка на проверку курсора по электронной почте ...", "configured_email": "Настройка электронной почты: {электронная почта}", "check_email_failed": "Проверка по электронной почте не удастся: {ошибка}", "verification_code": "Код проверки: {код}", "email_found": "Найдено электронное письмо с проверкой курсора"}}