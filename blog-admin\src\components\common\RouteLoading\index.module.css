.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  margin: 20px 0;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.tip {
  color: #666;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-top: 8px;
}

.progressContainer {
  width: 200px;
  margin-top: 8px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  animation: fadeIn 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    min-height: 300px;
    margin: 10px 0;
  }

  .content {
    padding: 30px 20px;
  }

  .progressContainer {
    width: 150px;
  }
}
