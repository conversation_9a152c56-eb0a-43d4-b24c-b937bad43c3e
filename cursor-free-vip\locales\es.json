{"menu": {"title": "Opciones Disponibles", "exit": "Salir del Programa", "reset": "Restablecer ID de Máquina", "register": "Registrar Nueva Cuenta de Cursor", "register_google": "Registrarse con Cuenta de Google", "register_github": "Registrarse con Cuenta de GitHub", "register_manual": "Registrar <PERSON>ursor con Correo Personalizado", "quit": "Cerrar Aplicación Cursor", "select_language": "Cambiar I<PERSON>", "input_choice": "Por favor, ingrese su elección ({choices})", "invalid_choice": "Selección inválida. Por favor ingrese un número de {choices}", "program_terminated": "El programa fue terminado por el usuario", "error_occurred": "Ocurrió un error: {error}. Por favor intente de nuevo", "press_enter": "Presione Enter para Salir", "disable_auto_update": "Deshabilitar Actualización Automática de Cursor", "lifetime_access_enabled": "ACCESO DE POR VIDA ACTIVADO", "totally_reset": "Restable<PERSON>", "outdate": "Desactualizado", "temp_github_register": "Registro Temporal de GitHub", "admin_required": "Ejecutando como ejecutable, se requieren privilegios de administrador.", "admin_required_continue": "Continuando sin privilegios de administrador.", "coming_soon": "Próximamente", "fixed_soon": "Arreglado Pronto", "contribute": "Contribuir al Proyecto", "config": "Mostrar Configuración", "delete_google_account": "Eliminar Cuenta Google de Cursor", "continue_prompt": "¿Continuar? (y/N): ", "operation_cancelled_by_user": "Operación cancelada por el usuario", "exiting": "Sal<PERSON>do ……", "bypass_version_check": "Omitir Verificación de Versión de Cursor", "check_user_authorized": "Verificar Usuario Autorizado", "bypass_token_limit": "Omitir l<PERSON> de tokens", "restore_machine_id": "Restaurar ID de máquina desde copia de seguridad", "select_chrome_profile": "Seleccionar perfil de Chrome", "language_config_saved": "Configuración del idioma guardada correctamente", "lang_invalid_choice": "Elección no válida. Ingrese una de las siguientes opciones: ({Lang_Choices})", "manual_custom_auth": "Autorización personalizada manual"}, "languages": {"ar": "<PERSON><PERSON><PERSON>", "en": "Inglés", "zh_cn": "简体中文", "zh_tw": "繁體中文", "vi": "Vietnamita", "nl": "Ho<PERSON><PERSON>", "de": "Alemán", "fr": "<PERSON><PERSON><PERSON><PERSON>", "pt": "Portugués", "ru": "<PERSON><PERSON><PERSON>", "tr": "<PERSON><PERSON><PERSON>", "bg": "Búlgaro", "es": "Español", "it": "italiano", "ja": "japonés"}, "quit_cursor": {"start": "Comenzando a Cerrar Cursor", "no_process": "No Hay Procesos de Cursor en Ejecución", "terminating": "Terminando Proceso {pid}", "waiting": "Esperando que el Proceso Termine", "success": "Todos los Procesos de Cursor Cerrados", "timeout": "Tiempo de Espera Agotado: {pids}", "error": "Ocurrió un Error: {error}"}, "reset": {"title": "Herramienta de Restablecimiento de ID de Máquina de Cursor", "checking": "Verificando Archivo de Configuración", "not_found": "Archivo de Configuración No Encontrado", "no_permission": "No se Puede Leer o Escribir el Archivo de Configuración, Verifique los Permisos", "reading": "Leyendo Configuración Actual", "creating_backup": "Creando Copia de Seguridad de la Configuración", "backup_exists": "El Archivo de Respaldo ya Existe, Omitiendo Paso de Respaldo", "generating": "Generando Nuevo ID de Máquina", "saving_json": "Guardando Nueva Configuración en JSON", "success": "ID de Máquina Restablecido Exitosamente", "new_id": "Nuevo ID de Máquina", "permission_error": "Erro<PERSON>: {error}", "run_as_admin": "Por Favor Intente Ejecutar Este Programa como Administrador", "process_error": "Error en el Proceso de Restablecimiento: {error}", "updating_sqlite": "Actualizando Base de Datos SQLite", "updating_pair": "Actualizando Par Clave-Valor", "sqlite_success": "Base de Datos SQLite Actualizada Exitosamente", "sqlite_error": "Falló la Actualización de la Base de Datos SQLite: {error}", "press_enter": "Presione Enter para Salir", "unsupported_os": "Sistema Operativo No Soportado: {os}", "linux_path_not_found": "Ruta de Linux No Encontrada", "updating_system_ids": "Actualizando IDs del Sistema", "system_ids_updated": "IDs del Sistema Actualizados Exitosamente", "system_ids_update_failed": "Falló la Actualización de IDs del Sistema: {error}", "windows_guid_updated": "GUID de Windows Actualizado Exitosamente", "windows_permission_denied": "Permisos de Windows Denegados", "windows_guid_update_failed": "Falló la Actualización del GUID de Windows", "macos_uuid_updated": "UUID de macOS Actualizado Exitosamente", "plutil_command_failed": "Falló el Comando plutil", "start_patching": "Iniciando Parcheo de getMachineId", "macos_uuid_update_failed": "Falló la Actualización del UUID de macOS", "current_version": "Versión Actual de Cursor: {version}", "patch_completed": "Parcheo de getMachineId Completado", "patch_failed": "Falló el Parcheo de getMachineId: {error}", "version_check_passed": "Verificación de Versión de Cursor Exitosa", "file_modified": "Archivo Modificado", "version_less_than_0_45": "Versión de Cursor < 0.45.0, Omitiendo Parcheo de getMachineId", "detecting_version": "Detectando Versión de Cursor", "patching_getmachineid": "<PERSON><PERSON><PERSON><PERSON>ne<PERSON>d", "version_greater_than_0_45": "Versión de Cursor >= 0.45.0, <PERSON><PERSON><PERSON><PERSON> getMachineId", "permission_denied": "<PERSON><PERSON><PERSON>: {error}", "backup_created": "Copia de Seguridad Creada", "update_success": "Actualización Exitosa", "update_failed": "Falló la Actualización: {error}", "windows_machine_guid_updated": "GUID de Máquina Windows Actualizado Exitosamente", "reading_package_json": "Leyendo package.json {path}", "invalid_json_object": "Objeto JSON Inválido", "no_version_field": "No se Encontró el Campo de Versión en package.json", "version_field_empty": "El Campo de Versión está Vacío", "invalid_version_format": "Formato de Versión Inválido: {version}", "found_version": "Versión Encontrada: {version}", "version_parse_error": "Error al Analizar <PERSON>: {error}", "package_not_found": "Package.json No Encontrado: {path}", "check_version_failed": "Falló la Verificación de Versión: {error}", "stack_trace": "Traza de la Pila", "version_too_low": "Versión de Cursor Muy Baja: {version} < 0.45.0", "no_write_permission": "Sin Permiso de Escritura: {path}", "path_not_found": "Ruta No Encontrada: {path}", "modify_file_failed": "Falló la Modificación del Archivo: {error}", "windows_machine_id_updated": "ID de Máquina Windows Actualizado Exitosamente", "update_windows_machine_id_failed": "Falló la Actualización del ID de Máquina Windows: {error}", "update_windows_machine_guid_failed": "Falló la Actualización del GUID de Máquina Windows: {error}", "file_not_found": "Archivo No Encontrado: {path}"}, "register": {"title": "Herramienta de Registro de Cursor", "start": "Iniciando proceso de registro...", "handling_turnstile": "Procesando verificación de seguridad...", "retry_verification": "Reintentando verificación...", "detect_turnstile": "Comprobando verificación de seguridad...", "verification_success": "Verificación de seguridad exitosa", "starting_browser": "Abriendo navegador...", "form_success": "Formulario enviado exitosamente", "browser_started": "Navegador abierto exitosamente", "waiting_for_second_verification": "Esperando verificación por correo electrónico...", "waiting_for_verification_code": "Esperando código de verificación...", "password_success": "Contraseña establecida exitosamente", "password_error": "No se pudo establecer la contraseña: {error}. Por favor intente de nuevo", "waiting_for_page_load": "Cargando página...", "first_verification_passed": "Verificación inicial exitosa", "mailbox": "Acceso exitoso a la bandeja de entrada", "register_start": "Iniciar <PERSON>", "form_submitted": "Formulario Enviado, Iniciando Verificación...", "filling_form": "Rellenando <PERSON>rio", "visiting_url": "Visitando URL", "basic_info": "Información Básica Enviada", "handle_turnstile": "<PERSON><PERSON><PERSON>", "no_turnstile": "No se Detectó Turnstile", "turnstile_passed": "Turnstile <PERSON>ado", "verification_start": "Comenzar a Obtener Código de Verificación", "verification_timeout": "Tiempo de Espera Agotado para Código de Verificación", "verification_not_found": "No se Encontró Código de Verificación", "try_get_code": "Intento | {attempt} Obtener Código de Verificación | Tiempo Restante: {time}s", "get_account": "Obteniendo Información de la Cuenta", "get_token": "Obtener Token de Sesión de Cursor", "token_success": "Token Obtenido Exitosamente", "token_attempt": "Intento | {attempt} veces para obtener Token | Se reintentará en {time}s", "token_max_attempts": "Alcanzado Máximo de Intentos ({max}) | No se pudo obtener Token", "token_failed": "<PERSON><PERSON> al Obtener Token: {error}", "account_error": "Falló al Obtener Información de la Cuenta: {error}", "press_enter": "Presione Enter para Salir", "browser_start": "<PERSON><PERSON><PERSON><PERSON>", "open_mailbox": "Abriendo Página de Correo", "email_error": "Falló al Obtener Dirección de Correo", "setup_error": "Error de Configuración de Correo: {error}", "start_getting_verification_code": "Comenzando a Obtener Código de Verificación, Se Intentará en 60s", "get_verification_code_timeout": "Tiempo de Espera Agotado para Código de Verificación", "get_verification_code_success": "Código de Verificación Obtenido Exitosamente", "try_get_verification_code": "Intento | {attempt} Obtener Código de Verificación | Tiempo Restante: {remaining_time}s", "verification_code_filled": "Código de Verificación Completado", "login_success_and_jump_to_settings_page": "Inicio de Sesión Exitoso y Salto a Página de Configuración", "detect_login_page": "Página de Inicio de Sesión Detectada, Iniciando Sesión...", "cursor_registration_completed": "¡Registro de Cursor Completado!", "set_password": "<PERSON><PERSON><PERSON>", "basic_info_submitted": "Información Básica Enviada", "cursor_auth_info_updated": "Información de Autenticación de Cursor Actualizada", "cursor_auth_info_update_failed": "Falló la Actualización de Información de Autenticación de Cursor", "reset_machine_id": "Restablecer ID de Máquina", "account_info_saved": "Información de Cuenta Guardada", "save_account_info_failed": "Falló al Guardar Información de Cuenta", "get_email_address": "Obtener Dirección de Correo", "update_cursor_auth_info": "Actualizar Información de Autenticación de Cursor", "register_process_error": "Error en el Proceso de Registro: {error}", "setting_password": "Estableciendo Contraseña", "manual_code_input": "Entrada Manual de Código", "manual_email_input": "Entrada Manual de Correo", "password": "Contraseña", "first_name": "Nombre", "last_name": "Apellido", "exit_signal": "Señal de Salida", "email_address": "Dirección de Correo", "config_created": "Configuración Creada", "verification_failed": "Verificación Fallida", "verification_error": "Error de Verificación: {error}", "config_option_added": "Opción de Configuración Añadida: {option}", "config_updated": "Configuración Actualizada", "password_submitted": "Contraseña Enviada", "total_usage": "Uso Total: {usage}", "setting_on_password": "Estableciendo Contraseña", "getting_code": "Obteniendo Código de Verificación, Se Intentará en 60s", "human_verify_error": "No se puede verificar que el usuario es humano. Reintentando...", "max_retries_reached": "Se alcanzó el máximo de intentos. Registro fallido.", "using_browser": "Usando {navegador} navegador: {ruta}", "could_not_track_processes": "No se pudo rastrear {navegador} procesos: {error}", "try_install_browser": "Intente instalar el navegador con su administrador de paquetes", "tempmail_plus_verification_started": "Iniciar proceso de verificación TempMailPlus", "tempmail_plus_enabled": "TempMailPlus está habilitado", "browser_path_invalid": "La ruta {navegador} no es válida, utilizando la ruta predeterminada", "using_tempmail_plus": "Uso de TempMailPlus para la verificación de correo electrónico", "tracking_processes": "Seguimiento {count} {navegador} procesos", "tempmail_plus_epin_missing": "TempMailPlus Epin no está configurado", "tempmail_plus_verification_failed": "Falló la verificación TempMailPlus: {Error}", "using_browser_profile": "Usando {navegador} perfil desde: {user_data_dir}", "tempmail_plus_verification_completed": "VERIFICACIÓN TEMPMAILPLUS completada con éxito", "tempmail_plus_email_missing": "El correo electrónico de TempMailPlus no está configurado", "tempmail_plus_init_failed": "No se pudo inicializar tempMailPlus: {error}", "tempmail_plus_config_missing": "Falta la configuración de TempMailPlus", "tempmail_plus_initialized": "TempMailPlus inicializado con éxito", "tempmail_plus_disabled": "TempMailPlus está deshabilitado", "no_new_processes_detected": "No hay nuevos procesos {navegador} detectados para rastrear", "make_sure_browser_is_properly_installed": "Asegúrese de que {navegador} esté instalado correctamente"}, "auth": {"title": "Administrador de Autenticación de Cursor", "checking_auth": "Verificando Archivo de Autenticación", "auth_not_found": "Archivo de Autenticación No Encontrado", "auth_file_error": "Error en Archivo de Autenticación: {error}", "reading_auth": "Leyendo Archivo de Autenticación", "updating_auth": "Actualizando Información de Autenticación", "auth_updated": "Información de Autenticación Actualizada Exitosamente", "auth_update_failed": "Falló la Actualización de Información de Autenticación: {error}", "auth_file_created": "Archivo de Autenticación Creado", "auth_file_create_failed": "Falló la Creación del Archivo de Autenticación: {error}", "press_enter": "Presione Enter para Salir", "reset_machine_id": "Restablecer ID de Máquina", "database_connection_closed": "Conexión a la Base de Datos Cerrada", "database_updated_successfully": "Base de Datos Actualizada Exitosamente", "connected_to_database": "Conectado a la Base de Datos", "updating_pair": "Actualizando Par Clave-Valor", "db_not_found": "Archivo de base de datos no encontrado en: {path}", "db_permission_error": "No se puede acceder al archivo de base de datos. Verifique los permisos", "db_connection_error": "Falló la conexión a la base de datos: {error}"}, "control": {"generate_email": "Generando Nuevo Correo", "blocked_domain": "Dominio <PERSON>", "select_domain": "Seleccionando Dominio <PERSON>", "copy_email": "<PERSON><PERSON>do Di<PERSON>cci<PERSON> de Correo", "enter_mailbox": "Entrando al Buzón de Correo", "refresh_mailbox": "Actualizando Buzón de Correo", "check_verification": "Verificando Código de Verificación", "verification_found": "Código de Verificación Encontrado", "verification_not_found": "No se Encontró Código de Verificación", "browser_error": "Error de Control del Navegador: {error}", "navigation_error": "Error de Navegación: {error}", "email_copy_error": "Error al Copiar Correo: {error}", "mailbox_error": "Error en el Buzón de Correo: {error}", "token_saved_to_file": "Token Guardado en cursor_tokens.txt", "navigate_to": "Navegando a {url}", "generate_email_success": "Generación de Correo Exitosa", "select_email_domain": "Seleccionar <PERSON>", "select_email_domain_success": "Selección de Dominio de Correo Exitosa", "get_email_name": "Obtener Nombre de Correo", "get_email_name_success": "Nombre de Correo Obtenido Exitosamente", "get_email_address": "Obtener Dirección de Correo", "get_email_address_success": "Dirección de Correo Obtenida Exitosamente", "enter_mailbox_success": "Entrada al Buzón de Correo Exitosa", "found_verification_code": "Código de Verificación Encontrado", "get_cursor_session_token": "Obtener Token de Sesión de Cursor", "get_cursor_session_token_success": "Token de Sesión de Cursor Obtenido Exitosamente", "get_cursor_session_token_failed": "Falló al Obtener Token de Sesión de Cursor", "save_token_failed": "Falló al Guardar Token", "database_updated_successfully": "Base de Datos Actualizada Exitosamente", "database_connection_closed": "Conexión a la Base de Datos Cerrada", "no_valid_verification_code": "No Hay Código de Verificación Válido"}, "email": {"starting_browser": "<PERSON><PERSON><PERSON><PERSON>", "visiting_site": "Visitando domini<PERSON> de correo", "create_success": "<PERSON><PERSON><PERSON> Exitosamente", "create_failed": "<PERSON>ó al Crear Correo", "create_error": "Error en la Creación del Correo: {error}", "refreshing": "Actualizando Correo", "refresh_success": "Correo Actualizado Exitosamente", "refresh_error": "Error al Actualizar Correo: {error}", "refresh_button_not_found": "Botón de Actualización No Encontrado", "verification_found": "Verificación Encontrada", "verification_not_found": "Verificación No Encontrada", "verification_error": "Error de Verificación: {error}", "verification_code_found": "Código de Verificación Encontrado", "verification_code_not_found": "Código de Verificación No Encontrado", "verification_code_error": "Error en el Código de Verificación: {error}", "address": "Dirección de Correo", "all_domains_blocked": "Todos los Dominios Bloqueados, <PERSON><PERSON><PERSON>", "no_available_domains_after_filtering": "No Hay Dominios Disponibles Después del Filtrado", "switching_service": "Cambiando al Servicio {service}", "domains_list_error": "Falló al Obtener Lista de Dominios: {error}", "failed_to_get_available_domains": "Falló al Obtener Dominios Disponibles", "domains_excluded": "Dominios Excluidos: {domains}", "failed_to_create_account": "Falló al Crear C<PERSON>ta", "account_creation_error": "Error en la Creación de la Cuenta: {error}", "blocked_domains": "Dominios Bloqueados: {domains}", "blocked_domains_loaded": "Dominios Bloqueados <PERSON>: {count}", "blocked_domains_loaded_error": "Error al Cargar Dominios Bloqueados: {error}", "blocked_domains_loaded_success": "Dominios Bloqueados Cargados Exitosamente", "blocked_domains_loaded_timeout": "Tiempo de Espera Agotado para Cargar Dominios Bloqueados: {timeout}s", "blocked_domains_loaded_timeout_error": "Error de Tiempo de Espera al Cargar Dominios Bloqueados: {error}", "available_domains_loaded": "Dominios Disponibles Cargados: {count}", "domains_filtered": "<PERSON><PERSON>os Filtrados: {count}", "trying_to_create_email": "Intentando crear correo: {email}", "domain_blocked": "Dominio Bloqueado: {domain}", "no_display_found": "No se encontró pantalla. Asegúrese de que X Server se esté ejecutando.", "try_export_display": "Prueba: Exportar pantalla =: 0", "try_install_chromium": "Prueba: Sudo Apt Instalar Chromium-Browser", "extension_load_error": "Error de carga de extensión: {error}", "make_sure_chrome_chromium_is_properly_installed": "Asegúrese de que el cromo/cromo esté instalado correctamente", "using_chrome_profile": "Usando el perfil de Chrome de: {user_data_dir}"}, "update": {"title": "Deshabilitar Actualización Automática de Cursor", "disable_success": "Actualización Automática Deshabilitada Exitosamente", "disable_failed": "Falló al Deshabilitar Actualización Automática: {error}", "press_enter": "Presione Enter para Salir", "start_disable": "Comenzar a Deshabilitar Actualización Automática", "killing_processes": "Terminando Procesos", "processes_killed": "Procesos Terminados", "removing_directory": "Eliminando Directorio", "directory_removed": "Directorio Eliminado", "creating_block_file": "Creando Archivo de Bloqueo", "block_file_created": "Archivo de Bloqueo Creado", "clearing_update_yml": "Limpiar el archivo Update.yml", "update_yml_cleared": "Archivo de Update.yml claro", "unsupported_os": "OS no compatible: {Sistema}", "block_file_already_locked": "El archivo de bloque ya está bloqueado", "yml_already_locked_error": "Update.yml File ya bloqueado Error: {Error}", "update_yml_not_found": "Archivo de Update.yml no encontrado", "yml_locked_error": "Update.yml Error bloqueado del archivo: {Error}", "remove_directory_failed": "No se pudo eliminar el directorio: {error}", "yml_already_locked": "El archivo Update.yml ya está bloqueado", "create_block_file_failed": "No se pudo crear un archivo de bloque: {error}", "block_file_locked_error": "Bloqueo Error bloqueado del archivo: {error}", "directory_locked": "El directorio está bloqueado: {ruta}", "block_file_already_locked_error": "Bloquee el archivo ya bloqueado Error: {Error}", "clear_update_yml_failed": "No se pudo borrar el archivo Update.yml: {Error}", "yml_locked": "El archivo de Update.yml está bloqueado", "block_file_locked": "El archivo de bloque está bloqueado"}, "updater": {"checking": "Buscando actualizaciones...", "new_version_available": "¡Nueva versión disponible! (Actual: {current}, Última: {latest})", "updating": "Actualizando a la última versión. El programa se reiniciará automáticamente.", "up_to_date": "Está utilizando la última versión.", "check_failed": "Falló al verificar actualizaciones: {error}", "continue_anyway": "Continuando con la versión actual...", "update_confirm": "¿Desea actualizar a la última versión? (Y/n)", "update_skipped": "Omitiendo actualización.", "invalid_choice": "Elección inválida. Por favor ingrese 'Y' o 'n'.", "development_version": "Versión de Desarrollo {current} > {latest}", "changelog_title": "Registro de Cambios", "rate_limit_exceeded": "Límite de velocidad de la API de GitHub excedido. Skinging actualización de actualización."}, "totally_reset": {"title": "Restable<PERSON>", "checking_config": "Verificando Archivo de Configuración", "config_not_found": "Archivo de Configuración No Encontrado", "no_permission": "No se Puede Leer o Escribir el Archivo de Configuración, Verifique los Permisos", "reading_config": "Leyendo Configuración Actual", "creating_backup": "Creando Copia de Seguridad de la Configuración", "backup_exists": "El Archivo de Respaldo ya Existe, Omitiendo Paso de Respaldo", "generating_new_machine_id": "Generando Nuevo ID de Máquina", "saving_new_config": "Guardando Nueva Configuración en JSON", "success": "Cursor Restablecido Exitosamente", "error": "Falló el Restablecimiento de Cursor: {error}", "press_enter": "Presione Enter para Salir", "reset_machine_id": "Restablecer ID de Máquina", "database_connection_closed": "Conexión a la Base de Datos Cerrada", "database_updated_successfully": "Base de Datos Actualizada Exitosamente", "connected_to_database": "Conectado a la Base de Datos", "updating_pair": "Actualizando Par Clave-Valor", "db_not_found": "Archivo de base de datos no encontrado en: {path}", "db_permission_error": "No se puede acceder al archivo de base de datos. Verifique los permisos", "db_connection_error": "Falló la conexión a la base de datos: {error}", "feature_title": "CARACTERÍSTICAS", "feature_1": "Eliminación completa de configuraciones y ajustes de Cursor AI", "feature_2": "Limpia todos los datos en caché incluyendo historial de IA y peticiones", "feature_3": "Restablece el ID de máquina para evitar la detección de prueba", "feature_4": "Crea nuevos identificadores de máquina aleatorios", "feature_5": "Elimina extensiones personalizadas y preferencias", "feature_6": "Restablece información de prueba y datos de activación", "feature_7": "Escaneo profundo de archivos ocultos relacionados con licencias y pruebas", "feature_8": "Preserva de forma segura archivos y aplicaciones que no son de Cursor", "feature_9": "Compatible con Windows, macOS y Linux", "disclaimer_title": "AVISO IMPORTANTE", "disclaimer_1": "Esta herramienta eliminará permanentemente todas las configuraciones de Cursor AI,", "disclaimer_2": "ajustes y datos en caché. Esta acción no se puede deshacer.", "disclaimer_3": "Sus archivos de código NO se verán afectados, y la herramienta está diseñada", "disclaimer_4": "para dirigirse solo a archivos del editor Cursor AI y mecanismos de detección de prueba.", "disclaimer_5": "Otras aplicaciones en su sistema no se verán afectadas.", "disclaimer_6": "Necesitará configurar Cursor AI nuevamente después de ejecutar esta herramienta.", "disclaimer_7": "Use bajo su propio riesgo", "confirm_title": "¿Está seguro de que desea continuar?", "confirm_1": "Esta acción eliminará todas las configuraciones de Cursor AI,", "confirm_2": "ajustes y datos en caché. Esta acción no se puede deshacer.", "confirm_3": "Sus archivos de código NO se verán afectados, y la herramienta está diseñada", "confirm_4": "para dirigirse solo a archivos del editor Cursor AI y mecanismos de detección de prueba.", "confirm_5": "Otras aplicaciones en su sistema no se verán afectadas.", "confirm_6": "Necesitará configurar Cursor AI nuevamente después de ejecutar esta herramienta.", "confirm_7": "Use bajo su propio riesgo", "invalid_choice": "Por favor ingrese 'Y' o 'n'", "skipped_for_safety": "Omitido por seguridad (no relacionado con Cursor): {path}", "deleted": "Eliminado: {path}", "error_deleting": "Error al eliminar {path}: {error}", "not_found": "Archivo no encontrado: {path}", "resetting_machine_id": "Restableciendo identificadores de máquina para evitar la detección de prueba...", "created_machine_id": "Creado nuevo ID de máquina: {path}", "error_creating_machine_id": "Error al crear archivo de ID de máquina {path}: {error}", "error_searching": "Error al buscar archivos en {path}: {error}", "created_extended_trial_info": "Creada nueva información de prueba extendida: {path}", "error_creating_trial_info": "Error al crear archivo de información de prueba {path}: {error}", "resetting_cursor_ai_editor": "Restableciendo Editor Cursor AI... Por favor espere.", "reset_cancelled": "Restablecimiento cancelado. Saliendo sin realizar cambios.", "windows_machine_id_modification_skipped": "Modificación de ID de máquina de Windows omitida: {error}", "linux_machine_id_modification_skipped": "Modificación de machine-id de Linux omitida: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Nota: El restablecimiento completo del ID de máquina puede requerir ejecutar como administrador", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Nota: El restablecimiento completo del machine-id del sistema puede requerir privilegios sudo", "windows_registry_instructions": "📝 NOTA: Para un restablecimiento completo en Windows, es posible que también deba limpiar entradas del registro.", "windows_registry_instructions_2": "   Ejecute 'regedit' y busque claves que contengan 'Cursor' o 'CursorAI' bajo HKEY_CURRENT_USER\\Software\\ y elimínelas.", "reset_log_1": "¡Cursor AI ha sido completamente restablecido y se ha evitado la detección de prueba!", "reset_log_2": "Por favor reinicie su sistema para que los cambios surtan efecto.", "reset_log_3": "Necesitará reinstalar Cursor AI y ahora debería tener un nuevo período de prueba.", "reset_log_4": "Para mejores resultados, considere tambi<PERSON>:", "reset_log_5": "Usar una dirección de correo diferente al registrarse para una nueva prueba", "reset_log_6": "Si está disponible, usar una VPN para cambiar su dirección IP", "reset_log_7": "Limpiar las cookies y caché de su navegador antes de visitar el sitio web de Cursor AI", "reset_log_8": "Si los problemas persisten, intente instalar Cursor AI en una ubicación diferente", "reset_log_9": "Si encuentra algún problema, vaya al Rastreador de Problemas de Github y cree un problema en https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Ocurrió un error inesperado: {error}", "report_issue": "Por favor reporte este problema al Rastreador de Problemas de Github en https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "Proceso interrumpido por el usuario. Saliendo...", "return_to_main_menu": "Volviendo al menú principal...", "process_interrupted": "Proceso interrumpido. Saliendo...", "press_enter_to_return_to_main_menu": "Presione Enter para volver al menú principal...", "removing_known": "Eliminando archivos conocidos de prueba/licencia", "performing_deep_scan": "Realizando escaneo profundo para archivos adicionales de prueba/licencia", "found_additional_potential_license_trial_files": "Se encontraron {count} archivos potenciales adicionales de licencia/prueba", "checking_for_electron_localstorage_files": "Verificando archivos de localStorage de Electron", "no_additional_license_trial_files_found_in_deep_scan": "No se encontraron archivos adicionales de licencia/prueba en el escaneo profundo", "removing_electron_localstorage_files": "Eliminando archivos de localStorage de Electron", "electron_localstorage_files_removed": "Archivos de localStorage de Electron eliminados", "electron_localstorage_files_removal_error": "Error al eliminar archivos de localStorage de Electron: {error}", "removing_electron_localstorage_files_completed": "Eliminación de archivos de localStorage de Electron completada", "warning_title": "ADVERTENCIA", "warning_1": "Esta acción eliminará todas las configuraciones de Cursor AI,", "warning_2": "ajustes y datos en caché. Esta acción no se puede deshacer.", "warning_3": "Sus archivos de código NO se verán afectados, y la herramienta está diseñada", "warning_4": "para dirigirse solo a archivos del editor Cursor AI y mecanismos de detección de prueba.", "warning_5": "Otras aplicaciones en su sistema no se verán afectadas.", "warning_6": "Necesitará configurar Cursor AI nuevamente después de ejecutar esta herramienta.", "warning_7": "Use bajo su propio riesgo", "removed": "Eliminado: {path}", "failed_to_reset_machine_guid": "Falló al restablecer GUID de máquina", "failed_to_remove": "Falló al eliminar: {path}", "failed_to_delete_file": "Falló al eliminar archivo: {path}", "failed_to_delete_directory": "Falló al eliminar directorio: {path}", "failed_to_delete_file_or_directory": "Falló al eliminar archivo o directorio: {path}", "deep_scanning": "Realizando escaneo profundo para archivos adicionales de prueba/licencia", "resetting_cursor": "Restableciendo Editor Cursor AI... Por favor espere.", "completed_in": "Completado en {time} segundos", "cursor_reset_completed": "¡El Editor Cursor AI ha sido completamente restablecido y se ha evitado la detección de prueba!", "cursor_reset_failed": "Falló el restablecimiento del Editor Cursor AI: {error}", "cursor_reset_cancelled": "Restablecimiento del Editor Cursor AI cancelado. Saliendo sin realizar cambios.", "operation_cancelled": "Operación cancelada. Saliendo sin realizar cambios.", "direct_advanced_navigation": "Intentar la navegación directa a la pestaña avanzada", "delete_input_error": "Error encontrar la entrada Eliminar: {error}", "delete_input_not_found_continuing": "Eliminar la entrada de confirmación no encontrada, tratando de continuar de todos modos", "advanced_tab_not_found": "Pestaña avanzada no encontrada después de múltiples intentos", "advanced_tab_error": "Error al encontrar la pestaña avanzada: {error}", "delete_input_not_found": "Eliminar la entrada de confirmación no encontrada después de múltiples intentos", "delete_input_retry": "Eliminar entrada no encontrada, intento {intento}/{max_attempts}", "login_redirect_failed": "Falló en la redirección de inicio de sesión, intentando la navegación directa ...", "advanced_tab_retry": "Pestaña avanzada no encontrada, intento {intento}/{max_attempts}", "advanced_tab_clicked": "Haga clic en la pestaña Avanzada", "already_on_settings": "Ya en la página de configuración", "found_danger_zone": "Sección de zona de peligro encontrado", "delete_button_retry": "Botón Eliminar no encontrado, intento {intento}/{max_attempts}", "delete_button_clicked": "Haga clic en el botón Eliminar la cuenta", "delete_button_not_found": "Eliminar el botón de cuenta no se encuentra después de múltiples intentos", "delete_button_error": "Error de encontrar el botón Eliminar: {error}", "navigating_to_settings": "Navegar a la página de configuración ..."}, "github_register": {"title": "Automatización de Registro de GitHub + Cursor AI", "features_header": "Características", "feature1": "Genera un correo temporal usando 1secmail.", "feature2": "Registra una nueva cuenta de GitHub con credenciales aleatorias.", "feature3": "Verifica el correo de GitHub automáticamente.", "feature4": "Inicia sesión en Cursor AI usando autenticación de GitHub.", "feature5": "Restablece el ID de máquina para evitar la detección de prueba.", "feature6": "Guarda todas las credenciales en un archivo.", "warnings_header": "Advertencias", "warning1": "Este script automatiza la creación de cuentas, lo que puede violar los términos de servicio de GitHub/Cursor.", "warning2": "Requiere acceso a internet y privilegios administrativos.", "warning3": "CAPTCHA o verificación adicional pueden interrumpir la automatización.", "warning4": "Use responsablemente y bajo su propio riesgo.", "confirm": "¿Está seguro de que desea continuar?", "invalid_choice": "Elección inválida. Por favor ingrese 'yes' o 'no'", "cancelled": "Operación cancelada", "program_terminated": "Programa terminado por el usuario", "starting_automation": "Iniciando automatización...", "github_username": "Nombre de Usuario de GitHub", "github_password": "Contraseña de GitHub", "email_address": "Dirección de Correo", "credentials_saved": "Estas credenciales han sido guardadas en github_cursor_accounts.txt", "completed_successfully": "¡Registro de GitHub + Cursor completado exitosamente!", "registration_encountered_issues": "El registro de GitHub + Cursor encontró problemas.", "check_browser_windows_for_manual_intervention_or_try_again_later": "Revise las ventanas del navegador para intervención manual o intente nuevamente más tarde."}, "chrome_profile": {"title": "Selección de Perfil de Chrome", "select_profile": "Seleccione un perfil de Chrome para usar:", "profile_list": "Perfiles disponibles:", "default_profile": "Perfil Predeterminado", "profile": "Perfil {number}", "no_profiles": "No se encontraron perfiles de Chrome", "error_loading": "Error al cargar perfiles de Chrome: {error}", "profile_selected": "Perfil seleccionado: {profile}", "invalid_selection": "Selección inválida. Por favor, intente de nuevo", "warning_chrome_close": "Advertencia: Esto cerrará todos los procesos de Chrome en ejecución"}, "restore": {"title": "Restaurar ID de máquina desde copia de seguridad", "starting": "Iniciando proceso de restauración de ID de máquina", "no_backups_found": "No se encontraron copias de seguridad", "available_backups": "Copias de seguridad disponibles", "select_backup": "Seleccione una copia de seguridad para restaurar", "to_cancel": "para cancelar", "operation_cancelled": "Operación cancelada", "invalid_selection": "Selección inválida", "please_enter_number": "Por favor, introduzca un número válido", "missing_id": "ID faltante: {id}", "read_backup_failed": "Error al leer el archivo de copia de seguridad: {error}", "current_file_not_found": "No se encontró el archivo de almacenamiento actual", "current_backup_created": "Se creó una copia de seguridad del archivo de almacenamiento actual", "storage_updated": "Archivo de almacenamiento actualizado con éxito", "update_failed": "Error al actualizar el archivo de almacenamiento: {error}", "sqlite_not_found": "No se encontró la base de datos SQLite", "updating_sqlite": "Actualizando base de datos SQLite", "updating_pair": "Actualizando par clave-valor", "sqlite_updated": "Base de datos SQLite actualizada con éxito", "sqlite_update_failed": "Error al actualizar la base de datos SQLite: {error}", "machine_id_backup_created": "Se creó una copia de seguridad del archivo machineId", "backup_creation_failed": "Error al crear la copia de seguridad: {error}", "machine_id_updated": "Archivo machineId actualizado con éxito", "machine_id_update_failed": "Error al actualizar el archivo machineId: {error}", "updating_system_ids": "Actualizando IDs del sistema", "system_ids_update_failed": "Error al actualizar los IDs del sistema: {error}", "permission_denied": "Permiso denegado. Intente ejecutar como administrador", "windows_machine_guid_updated": "GUID de máquina Windows actualizado con éxito", "update_windows_machine_guid_failed": "Error al actualizar el GUID de máquina Windows: {error}", "windows_machine_id_updated": "ID de máquina Windows actualizado con éxito", "update_windows_machine_id_failed": "Error al actualizar el ID de máquina Windows: {error}", "sqm_client_key_not_found": "No se encontró la clave de registro SQMClient", "update_windows_system_ids_failed": "Error al actualizar los IDs del sistema Windows: {error}", "macos_platform_uuid_updated": "UUID de plataforma macOS actualizado con éxito", "failed_to_execute_plutil_command": "Error al ejecutar el comando plutil", "update_macos_system_ids_failed": "Error al actualizar los IDs del sistema macOS: {error}", "ids_to_restore": "IDs de máquina a restaurar", "confirm": "¿Está seguro de que desea restaurar estos IDs?", "success": "ID de máquina restaurado con éxito", "process_error": "Error en el proceso de restauración: {error}", "press_enter": "Presione Enter para continuar"}, "oauth": {"no_chrome_profiles_found": "No se encontraron perfiles de Chrome, utilizando el valor predeterminado", "starting_new_authentication_process": "Iniciar nuevo proceso de autenticación ...", "failed_to_delete_account": "No se pudo eliminar la cuenta: {error}", "found_email": "Correo electrónico encontrado: {correo electrónico}", "github_start": "<PERSON><PERSON><PERSON><PERSON>", "already_on_settings_page": "¡Ya en la página Configuración!", "starting_github_authentication": "Comenzar la autenticación de Github ...", "status_check_error": "Error de verificación de estado: {error}", "account_is_still_valid": "La cuenta sigue siendo válida (uso: {uso})", "authentication_timeout": "Tiempo de espera de autenticación", "google_start": "Inicio de Google", "usage_count": "Recuento de uso: {uso}", "using_first_available_chrome_profile": "Usando el primer perfil de Chrome disponible: {perfil}", "no_compatible_browser_found": "No se encontró un navegador compatible. Instale Google Chrome o Chromium.", "authentication_successful_getting_account_info": "Autenticación exitosa, obteniendo información de cuenta ...", "found_chrome_at": "Encontrado Chrome en: {ruta}", "error_getting_user_data_directory": "Error al obtener directorio de datos de usuario: {error}", "error_finding_chrome_profile": "Error de encontrar el perfil de Chrome, usando el valor predeterminado: {error}", "auth_update_success": "El éxito de la actualización de la autenticación", "authentication_successful": "Autenticación exitosa - correo electrónico: {correo electrónico}", "authentication_failed": "La autenticación falló: {error}", "warning_browser_close": "Advertencia: esto cerrará todos los procesos en ejecución {navegador}", "supported_browsers": "Navegadores compatibles para {plataforma}", "authentication_button_not_found": "Botón de autenticación no se encuentra", "starting_new_google_authentication": "Iniciar nueva autenticación de Google ...", "waiting_for_authentication": "Esperando la autenticación ...", "found_default_chrome_profile": "Perfil de Chrome predeterminado encontrado encontrado", "starting_browser": "Browser inicial en: {ruta}", "token_extraction_error": "Error de extracción de token: {error}", "could_not_check_usage_count": "No pudo verificar el recuento de uso: {error}", "profile_selection_error": "Error durante la selección de perfil: {error}", "warning_could_not_kill_existing_browser_processes": "ADVERTENCIA: No se pudo matar los procesos existentes del navegador: {Error}", "browser_failed_to_start": "El navegador no pudo comenzar: {error}", "redirecting_to_authenticator_cursor_sh": "Redirección a autenticador.cursor.sh ...", "found_browser_data_directory": "Directorio de datos del navegador encontrado: {ruta}", "browser_not_found_trying_chrome": "No pudo encontrar {navegador}, intentando Chrome en su lugar", "starting_re_authentication_process": "Inicio del proceso de reautenticación ...", "found_cookies": "Encontrado {Count} Cookies", "auth_update_failed": "La actualización de la autenticación falló", "browser_failed_to_start_fallback": "El navegador no pudo comenzar: {error}", "failed_to_delete_expired_account": "No se pudo eliminar la cuenta vencida", "navigating_to_authentication_page": "Navegar a la página de autenticación ...", "browser_closed": "Navegador cerrado", "failed_to_delete_account_or_re_authenticate": "No se pudo eliminar la cuenta o reautenticar: {error}", "initializing_browser_setup": "Inicializar la configuración del navegador ...", "failed_to_extract_auth_info": "No se pudo extraer información de autenticación: {error}", "detected_platform": "Plataforma detectada: {plataforma}", "starting_google_authentication": "Iniciar autenticación de Google ...", "browser_failed": "El navegador no pudo comenzar: {error}", "using_browser_profile": "Usando el perfil del navegador: {perfil}", "consider_running_without_sudo": "Considere ejecutar el script sin sudo", "try_running_without_sudo_admin": "Intente ejecutar sin privilegios de sudo/administrador", "running_as_root_warning": "No se recomienda ejecutar la raíz para la automatización del navegador", "page_changed_checking_auth": "Cambiado en la página, controlando la autenticación ...", "please_select_your_google_account_to_continue": "Seleccione su cuenta de Google para continuar ...", "browser_setup_failed": "Falló la configuración del navegador: {error}", "missing_authentication_data": "Datos de autenticación faltantes: {datos}", "using_configured_browser_path": "<PERSON><PERSON> de la ruta configurada {navegador}: {ruta}", "could_not_find_usage_count": "No pudo encontrar el recuento de uso: {error}", "killing_browser_processes": "Matar {navegador} procesos ...", "browser_setup_completed": "Configuración del navegador completada con éxito", "account_has_reached_maximum_usage": "La cuenta ha alcanzado el uso máximo, {eliminar}", "could_not_find_email": "No pudo encontrar correo electrónico: {error}", "user_data_dir_not_found": "{navegador} Directorio de datos de usuario que no se encuentra en {ruta}, intentará Chrome en su lugar", "found_browser_user_data_dir": "Directorio de datos de usuario encontrado {navegador}: {ruta}", "invalid_authentication_type": "Tipo de autenticación no válido"}, "manual_auth": {"auth_type_selected": "Tipo de autenticación seleccionado: {tipo}", "proceed_prompt": "¿Proceder? (S/N):", "auth_type_github": "<PERSON><PERSON><PERSON>", "confirm_prompt": "Confirme la siguiente información:", "invalid_token": "Token inválido. Autenticación abortada.", "continue_anyway": "¿Continuar de todos modos? (S/N):", "token_verified": "Token Verificado con éxito!", "error": "Error: {error}", "auth_update_failed": "No se pudo actualizar la información de autenticación", "auth_type_prompt": "Seleccione Tipo de autenticación:", "auth_type_auth0": "Auth_0 (predeterminado)", "verifying_token": "Verificar la validez del token ...", "auth_updated_successfully": "Información de autenticación actualizada con éxito!", "email_prompt": "Ingrese el correo electrónico (deje en blanco para un correo electrónico aleatorio):", "token_prompt": "Ingrese el token de su cursor (access_token/refresh_token):", "title": "Autenticación del cursor manual", "token_verification_skipped": "Verificación del token omitido (check_user_authorized.py no encontrado)", "random_email_generated": "Correo electrónico aleatorio generado: {correo electrónico}", "token_required": "Se requiere token", "auth_type_google": "Google", "operation_cancelled": "Operación cancelada", "token_verification_error": "Error de verificación de token: {error}", "updating_database": "Actualización de la base de datos de autenticación del cursor ..."}, "auth_check": {"token_length": "Longitud del token: {longitud} caracteres", "usage_response_status": "Estado de respuesta de uso: {respuesta}", "operation_cancelled": "Operación cancelada por el usuario", "error_getting_token_from_db": "Error al obtener token de la base de datos: {error}", "checking_usage_information": "Verificación de información de uso ...", "usage_response": "Respuesta de uso: {respuesta}", "authorization_failed": "¡Falló la autorización!", "authorization_successful": "Autorización exitosa!", "request_timeout": "Solicitar el tiempo de tiempo fuera", "check_error": "Error de comprobación de autorización: {error}", "connection_error": "E<PERSON>r de conexión", "invalid_token": "To<PERSON> in<PERSON>lid<PERSON>", "check_usage_response": "Verifique el uso de la respuesta: {Respuesta}", "enter_token": "Ingrese el token de su cursor:", "token_found_in_db": "Token encontrado en la base de datos", "user_unauthorized": "El usuario no está autorizado", "checking_authorization": "Verificación de autorización ...", "error_generating_checksum": "Error de generación de la suma de verificación: {error}", "token_source": "¿Obtener token de la base de datos o la entrada manualmente? (D/M, predeterminado: D)", "unexpected_error": "Error inesperado: {error}", "user_authorized": "El usuario está autorizado", "token_not_found_in_db": "Token no encontrado en la base de datos", "jwt_token_warning": "El token parece estar en formato JWT, pero la comprobación de API devolvió un código de estado inesperado. El token puede ser válido, pero el acceso a la API está restringido.", "unexpected_status_code": "Código de estado inesperado: {código}", "getting_token_from_db": "Obtener token de la base de datos ...", "cursor_acc_info_not_found": "cursor_acc_info.py no encontrado"}, "account_delete": {"delete_input_not_found": "Eliminar la entrada de confirmación no encontrada después de múltiples intentos", "logging_in": "Iniciar sesi<PERSON> con Google ...", "confirm_button_not_found": "Confirmar el botón no encontrado después de múltiples intentos", "confirm_button_error": "Error de encontrar el botón Confirmar: {Error}", "delete_button_clicked": "Haga clic en el botón Eliminar la cuenta", "confirm_prompt": "¿Estás seguro de que quieres continuar? (S/N):", "delete_button_error": "Error de encontrar el botón Eliminar: {error}", "cancelled": "Eliminación de la cuenta cancelada.", "error": "Error durante la eliminación de la cuenta: {error}", "interrupted": "Proceso de eliminación de la cuenta interrumpido por el usuario.", "delete_input_not_found_continuing": "Eliminar la entrada de confirmación no encontrada, tratando de continuar de todos modos", "advanced_tab_retry": "Pestaña avanzada no encontrada, intento {intento}/{max_attempts}", "waiting_for_auth": "Esperando la autenticación de Google ...", "typed_delete": "\"Eliminar\" mecanografiado en el cuadro de confirmación", "trying_settings": "Tratando de navegar a la página de configuración ...", "delete_input_retry": "Eliminar entrada no encontrada, intento {intento}/{max_attempts}", "email_not_found": "Correo electrónico no encontrado: {error}", "delete_button_not_found": "Eliminar el botón de cuenta no se encuentra después de múltiples intentos", "already_on_settings": "Ya en la página de configuración", "failed": "El proceso de eliminación de la cuenta falló o fue cancelado.", "warning": "Advertencia: esto eliminará permanentemente su cuenta de cursor. Esta acción no se puede deshacer.", "direct_advanced_navigation": "Intentar la navegación directa a la pestaña avanzada", "advanced_tab_not_found": "Pestaña avanzada no encontrada después de múltiples intentos", "auth_timeout": "Tiempo de espera de autenticación, continuando de todos modos ...", "select_google_account": "Seleccione su cuenta de Google ...", "google_button_not_found": "El botón de inicio de sesión de Google no se encuentra", "found_danger_zone": "Sección de zona de peligro encontrado", "account_deleted": "Cuenta eliminada con éxito!", "starting_process": "Proceso de eliminación de la cuenta inicial ...", "advanced_tab_error": "Error al encontrar la pestaña avanzada: {error}", "delete_button_retry": "Botón Eliminar no encontrado, intento {intento}/{max_attempts}", "login_redirect_failed": "Falló en la redirección de inicio de sesión, intentando la navegación directa ...", "unexpected_error": "Error inesperado: {error}", "delete_input_error": "Error encontrar la entrada Eliminar: {error}", "login_successful": "Iniciar <PERSON><PERSON><PERSON> exitoso", "advanced_tab_clicked": "Haga clic en la pestaña Avanzada", "unexpected_page": "Página inesperada después del inicio de sesión: {URL}", "found_email": "Correo electrónico encontrado: {correo electrónico}", "title": "Herramienta de eliminación de la cuenta de cursor de Google", "navigating_to_settings": "Navegar a la página de configuración ...", "success": "¡Su cuenta de cursor se ha eliminado con éxito!", "confirm_button_retry": "Confirmar el botón no encontrado, intento {intento}/{max_attempts}"}, "token": {"refreshing": "Refrescante token ...", "extraction_error": "Error de extraer token: {error}", "invalid_response": "Respuesta JSON no válida del servidor de actualización", "no_access_token": "No hay token de acceso en respuesta", "connection_error": "Error de conexión para actualizar el servidor", "unexpected_error": "Error inesperado durante la actualización del token: {error}", "server_error": "Actualizar el error del servidor: http {status}", "refresh_success": "Token renovado con éxito! Válido para {días} días (expiras: {expirar})", "request_timeout": "Solicitud para actualizar el horario del servidor", "refresh_failed": "Falló en la actualización del token: {error}"}, "browser_profile": {"profile_selected": "Perfil seleccionado: {perfil}", "default_profile": "Perfil predeterminado", "no_profiles": "No se encontraron perfiles {navegador}", "select_profile": "Seleccione el perfil {navegador} para usar:", "error_loading": "Error de carga {navegador} perfiles: {error}", "invalid_selection": "Selección no válida. Por favor intente de nuevo.", "title": "Selección de perfil de navegador", "profile": "Perfil {número}", "profile_list": "Disponible {navegador} perfiles:"}, "account_info": {"subscription": "Suscripción", "failed_to_get_account_info": "No se pudo obtener información de la cuenta", "subscription_type": "Tipo de suscripción", "pro": "Pro", "failed_to_get_account": "No se pudo obtener información de la cuenta", "config_not_found": "Configuración no encontrada.", "premium_usage": "Uso de primas", "failed_to_get_subscription": "No se pudo obtener información de suscripción", "basic_usage": "Uso básico", "premium": "De primera calidad", "free": "<PERSON><PERSON><PERSON>", "email_not_found": "Correo electrónico no encontrado", "title": "Información de la cuenta", "inactive": "Inactivo", "remaining_trial": "Prueba restante", "enterprise": "Empresa", "lifetime_access_enabled": "Acceso de por vida habilitado", "failed_to_get_usage": "No se pudo obtener información de uso", "usage_not_found": "Uso no encontrado", "days_remaining": "Días restantes", "failed_to_get_token": "No se pudo hacer token", "token": "Simbólico", "subscription_not_found": "Información de suscripción no encontrada", "days": "días", "team": "Equipo", "token_not_found": "Token no encontrado", "pro_trial": "Prueba pro", "email": "Correo electrónico", "active": "Activo", "failed_to_get_email": "No se pudo obtener la dirección de correo electrónico", "trial_remaining": "Prueba profesional restante", "usage": "<PERSON><PERSON>"}, "config": {"config_updated": "Configuración actualizada", "configuration": "Configuración", "file_owner": "Propietario del archivo: {propietario}", "error_checking_linux_paths": "Error de comprobación de rutas de Linux: {error}", "storage_file_is_empty": "El archivo de almacenamiento está vacío: {storage_path}", "config_directory": "Directorio de configuración", "documents_path_not_found": "Ruta de documentos no encontrado, utilizando el directorio actual", "config_not_available": "Configuración no disponible", "neither_cursor_nor_cursor_directory_found": "Ni el cursor ni el directorio cursor se encuentran en {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Asegúrese de que el cursor esté instalado y se haya ejecutado al menos una vez", "config_created": "Config creado: {config_file}", "using_temp_dir": "Usando directorio temporal debido al error: {ruta} (error: {error})", "storage_file_not_found": "Archivo de almacenamiento no encontrado: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "El archivo puede estar dañado, reinstale el cursor", "error_getting_file_stats": "Error al obtener estadísticas de archivo: {error}", "enabled": "Activado", "backup_created": "Copia de seguridad creada: {ruta}", "file_permissions": "Permisos de archivo: {permisos}", "config_setup_error": "Error de configuración de configuración: {error}", "config_removed": "Archivo de configuración eliminado para la actualización forzada", "config_force_update_enabled": "Actualización de la fuerza de archivo de configuración habilitada, realizando la actualización forzada", "file_size": "Tamaño del archivo: {size} bytes", "error_reading_storage_file": "Error al leer el archivo de almacenamiento: {error}", "config_force_update_disabled": "Actualización de la fuerza de archivo de configuración deshabilitado, omitiendo la actualización forzada", "config_dir_created": "Directorio de configuración creado: {ruta}", "config_option_added": "Opción de configuración agregada: {opción}", "file_group": "Grupo de archivos: {grupo}", "and": "Y", "backup_failed": "No se pudo hacer una copia de seguridad de la configuración: {error}", "force_update_failed": "Falló de configuración de actualización de fuerza: {error}", "storage_directory_not_found": "Directorio de almacenamiento no encontrado: {Storage_dir}", "also_checked": "También verificado {ruta}", "try_running": "Intente ejecutar: {comando}", "disabled": "Desactivado", "storage_file_found": "Archivo de almacenamiento encontrado: {storage_path}", "storage_file_is_valid_and_contains_data": "El archivo de almacenamiento es válido y contiene datos", "permission_denied": "Permiso denegado: {Storage_Path}"}, "bypass": {"found_product_json": "Product.json encontrado: {ruta}", "starting": "Inicio de la versión del cursor Bypass ...", "version_updated": "Versión actualizada de {Old} a {new}", "menu_option": "Verificación de la versión del cursor de derivación", "unsupported_os": "Sistema operativo no compatible: {Sistema}", "backup_created": "Copia de seguridad creada: {ruta}", "current_version": "Versión actual: {versión}", "localappdata_not_found": "Variable de entorno LocalAppdata no encontrada", "no_write_permission": "Sin permiso de escritura para el archivo: {ruta}", "write_failed": "No se pudo escribir Product.json: {Error}", "description": "Esta herramienta modifica el producto de cursor.json para evitar restricciones de versión", "bypass_failed": "Versión Bypass falló: {error}", "title": "Herramienta de derivación de la versión del cursor", "no_update_needed": "No se necesita actualización. La versión actual {versión} ya es> = 0.46.0", "read_failed": "No se pudo leer Product.json: {Error}", "stack_trace": "Rastro de pila", "product_json_not_found": "Product.json no se encuentra en las rutas de Linux comunes", "file_not_found": "Archivo no encontrado: {ruta}"}, "bypass_token_limit": {"description": "Esta herramienta modifica el archivo workbench.desktop.main.js para evitar el límite del token", "press_enter": "Presione Entrar para continuar ...", "title": "Herramienta de límite de token de derivación"}, "tempmail": {"general_error": "Se produjo un error: {error}", "config_error": "Error de archivo de configuración: {error}", "no_email": "No se encuentra el correo electrónico de verificación del cursor", "checking_email": "Comprobación del correo electrónico de verificación del cursor ...", "configured_email": "Correo electrónico configurado: {correo electrónico}", "extract_code_failed": "Extraer el código de verificación fallido: {error}", "no_code": "No pudo obtener el código de verificación", "check_email_failed": "Verifique el correo electrónico fallido: {error}", "email_found": "Correo electrónico de verificación del cursor encontrado", "verification_code": "Código de verificación: {código}"}}