{"menu": {"title": "Beschikbare Opties", "exit": "<PERSON><PERSON>", "reset": "Machine ID Resetten", "register": "<PERSON><PERSON><PERSON> Cursor Account <PERSON>ren", "register_google": "Registreren met Google Account", "register_github": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON> Account", "register_manual": "<PERSON><PERSON><PERSON> met Aangepaste E-mail", "quit": "<PERSON><PERSON><PERSON>", "select_language": "Taal Wijzigen", "select_chrome_profile": "Chrome Profiel <PERSON>n", "input_choice": "<PERSON><PERSON><PERSON> uw keuze in ({choices})", "invalid_choice": "Ongeldige selectie. Voer een nummer in uit {choices}.", "program_terminated": "<PERSON><PERSON> is be<PERSON><PERSON><PERSON><PERSON> door de gebruiker", "error_occurred": "Er is een fout opgetreden: {error}. Probeer het opnieuw.", "press_enter": "<PERSON>uk op Enter om door te gaan.", "disable_auto_update": "Cursor automatische updates uitschakelen", "lifetime_access_enabled": "<PERSON><PERSON><PERSON><PERSON> toegang ingeschakeld", "totally_reset": "<PERSON><PERSON>or volledig resetten", "outdate": "Vero<PERSON><PERSON>", "temp_github_register": "Tijdelijke GitHub-registratie", "coming_soon": "Binnenkort", "fixed_soon": "Binnenkort Opgelost", "contribute": "Bijdragen aan het Project", "config": "Configurat<PERSON>", "delete_google_account": "Cursor Google Account Verwijderen", "continue_prompt": "Doorgaan? (y/N): ", "operation_cancelled_by_user": "<PERSON>tie geannuleerd door gebruiker", "exiting": "Afsluiten ……", "bypass_version_check": "<PERSON><PERSON><PERSON> Overslaan", "check_user_authorized": "Gebruikersautorisatie Controleren", "bypass_token_limit": "Token-limiet omzeilen", "restore_machine_id": "Machine-ID <PERSON><PERSON><PERSON> vanaf backup", "admin_required": "Uitvoeren als uitvo<PERSON>ar, vereiste beheerdersrechten.", "language_config_saved": "Taalconfigurat<PERSON> met succes opgeslagen", "lang_invalid_choice": "Ongeldige keuze. Voer een van de volgende opties in: ({lang_choices}))", "manual_custom_auth": "Handmatige aangepaste auth", "admin_required_continue": "Doorgaan zonder beheerdersrechten."}, "languages": {"ar": "Arabisch", "en": "<PERSON><PERSON><PERSON>", "zh_cn": "Vereenvoudigd Chinees", "zh_tw": "Traditioneel <PERSON>", "vi": "Vietnamees", "nl": "Nederlands", "de": "<PERSON><PERSON>", "fr": "<PERSON><PERSON>", "pt": "Portugees", "ru": "<PERSON><PERSON>", "es": "Spaans", "bg": "Bulgaars", "tr": "Turks", "it": "Italiaans", "ja": "Japanse"}, "quit_cursor": {"start": "Start met <PERSON><PERSON><PERSON><PERSON><PERSON>", "no_process": "<PERSON><PERSON> actief Cursor-proces", "terminating": "Proces beëindigen {pid}", "waiting": "Wachten tot het proces is afgesloten", "success": "Alle Cursor-processen gesloten", "timeout": "Proces time-out: {pids}", "error": "Fout opgetreden: {error}"}, "reset": {"title": "Cursor Machine-ID Reset Tool", "checking": "Configuratiebestand controleren", "not_found": "Configuratiebestand niet gevonden", "no_permission": "<PERSON><PERSON> configurat<PERSON><PERSON><PERSON>d niet lezen of s<PERSON><PERSON><PERSON><PERSON>, controleer de bestands<PERSON>en", "reading": "Huidige configuratie lezen", "creating_backup": "Configuratieback-up maken", "backup_exists": "Back-upbestand bestaat al, back-upstap overslaan", "generating": "Nieuwe Machine-ID genereren", "saving_json": "Nieuwe configuratie opslaan naar JSON", "success": "Machine-ID succesvol gereset", "new_id": "Nieuwe Machine-ID", "permission_error": "Toestemmingsfout: {error}", "run_as_admin": "<PERSON><PERSON>r dit programma als beheerder uit te voeren", "process_error": "Resetprocesfout: {error}", "updating_sqlite": "SQLite-database bijwerken", "updating_pair": "Sleutel-waarde paar bijwerken", "sqlite_success": "SQLite-database succesvol bijgewerkt", "sqlite_error": "SQLite-database bijwerken mislukt: {error}", "press_enter": "<PERSON>uk op Enter om door te gaan", "unsupported_os": "Niet-ondersteund besturingssysteem: {os}", "linux_path_not_found": "Linux-pad niet gevonden", "updating_system_ids": "Systeem-ID's bijwerken", "system_ids_updated": "Systeem-ID's succesvol bijgewerkt", "system_ids_update_failed": "Systeem-ID's bijwerken mislukt: {error}", "windows_guid_updated": "Windows GUID succesvol bijgewerkt", "windows_permission_denied": "Windows toestemming geweigerd", "windows_guid_update_failed": "Windows GUID bijwerken mislukt", "macos_uuid_updated": "macOS UUID succesvol bijgewerkt", "plutil_command_failed": "plutil-op<PERSON><PERSON> mislukt", "start_patching": "Patching getMachineId starten", "macos_uuid_update_failed": "macOS UUID bijwerken mislukt", "current_version": "<PERSON><PERSON><PERSON>-versie: {version}", "patch_completed": "Patching getMachineId voltooid", "patch_failed": "Patching getMachineId mislukt: {error}", "version_check_passed": "Cursor-versiecontrole geslaagd", "file_modified": "<PERSON><PERSON> gewij<PERSON>d", "version_less_than_0_45": "Cursor-versie < 0.45.0, patching getMachineId overslaan", "detecting_version": "Cursor-versie detecteren", "patching_getmachineid": "Patching getMachineId", "version_greater_than_0_45": "Cursor-versie >= 0.45.0, patching getMachineId", "permission_denied": "Toestemming geweigerd: {error}", "backup_created": "Back-up g<PERSON><PERSON><PERSON>", "update_success": "Update geslaagd", "update_failed": "Update mislukt: {error}", "windows_machine_guid_updated": "Windows Machine GUID succesvol bijgewerkt", "reading_package_json": "package.json lezen {path}", "invalid_json_object": "Ongeldig JSON-object", "no_version_field": "<PERSON><PERSON> versieveld gevonden in package.json", "version_field_empty": "Versieveld is leeg", "invalid_version_format": "Ongeldig versieformaat: {version}", "found_version": "Gevonden versie: {version}", "version_parse_error": "Versie parse-fout: {error}", "package_not_found": "Package.json niet gevonden: {path}", "check_version_failed": "Versiecontrole mislukt: {error}", "stack_trace": "Stack Trace", "version_too_low": "Cursor-versie te laag: {version} < 0.45.0", "no_write_permission": "<PERSON><PERSON> s<PERSON>ri<PERSON>: {path}", "path_not_found": "Pad niet gevonden: {path}", "modify_file_failed": "Bestand wijzigen mislukt: {error}", "windows_machine_id_updated": "Windows Machine-ID succesvol bijgewerkt", "update_windows_machine_id_failed": "Windows Machine-ID bijwerken mislukt: {error}", "update_windows_machine_guid_failed": "Windows Machine GUID bijwerken mislukt: {error}", "file_not_found": "<PERSON>and niet gevonden: {path}"}, "register": {"title": "Cursor Registratietool", "start": "Registratieproces starten...", "handling_turnstile": "Beveiligingsverificatie verwerken...", "retry_verification": "Verificatie opnieuw proberen...", "detect_turnstile": "Beveiligingsverificatie controleren...", "verification_success": "Beveiligingsverificatie geslaagd", "starting_browser": "Browser openen...", "form_success": "Formulier succesvol ingediend", "browser_started": "Browser succesvol geopend", "waiting_for_second_verification": "Wachten op e-mailverificatie...", "waiting_for_verification_code": "Wachten op verificatiecode...", "password_success": "Wachtwoord succesvol ingesteld", "password_error": "Kon wachtwoord niet instellen: {error}. Probeer het opnieuw", "waiting_for_page_load": "Pagina laden...", "first_verification_passed": "Eerste verificatie geslaagd", "mailbox": "E-mailinbox succesvol geopend", "register_start": "Registratie starten", "form_submitted": "<PERSON><PERSON><PERSON> ingediend, verificatie starten...", "filling_form": "<PERSON><PERSON><PERSON> invu<PERSON>", "visiting_url": "URL bezoeken", "basic_info": "Basisinformatie ingediend", "handle_turnstile": "Turnstile verwerken", "no_turnstile": "<PERSON><PERSON> g<PERSON>teerd", "turnstile_passed": "<PERSON><PERSON><PERSON> g<PERSON>agd", "verification_start": "Verificatiecode verkrijgen starten", "verification_timeout": "Verificatiecode time-out", "verification_not_found": "Geen verificatiecode gevonden", "try_get_code": "Probeer | {attempt} Verificatiecode verkrijgen | Tijd over: {time}s", "get_account": "Accountinformatie verkrijgen", "get_token": "Cursor-sessietoken verkrijgen", "token_success": "Token succesvol verkregen", "token_attempt": "Probeer | {attempt} keer om token te verkrijgen | Opnieuw proberen in {time}s", "token_max_attempts": "Maximale pogingen bereikt ({max}) | Token verkrijgen mislukt", "token_failed": "Token verkrijgen mislukt: {error}", "account_error": "Accountinformatie verkrijgen mislukt: {error}", "press_enter": "<PERSON>uk op Enter om door te gaan", "browser_start": "Browser starten", "open_mailbox": "Mailboxpagina openen", "email_error": "E-mailadres verkrijgen mislukt", "setup_error": "E-mailinstelling fout: {error}", "start_getting_verification_code": "Verificatiecode verkrijgen starten, opnieuw proberen in 60s", "get_verification_code_timeout": "Verificatiecode verkrijgen time-out", "get_verification_code_success": "Verificatiecode succesvol verkregen", "try_get_verification_code": "Probeer | {attempt} Verificatiecode verkrijgen | Tijd over: {remaining_time}s", "verification_code_filled": "Verificatiecode ingevuld", "login_success_and_jump_to_settings_page": "Inloggen geslaagd en naar instellingenpagina springen", "detect_login_page": "Inlogpagina detecteren, inloggen starten...", "cursor_registration_completed": "Cursor-registratie voltooid!", "set_password": "Wachtwoord instellen", "basic_info_submitted": "Basisinformatie ingediend", "cursor_auth_info_updated": "Cursor-authenticatie-informatie bijgewerkt", "cursor_auth_info_update_failed": "Cursor-authenticatie-informatie bijwerken mislukt", "reset_machine_id": "Machine-ID resetten", "account_info_saved": "Accountinformatie opgeslagen", "save_account_info_failed": "Accountinformatie opslaan mislukt", "get_email_address": "E-mailadres verkrijgen", "update_cursor_auth_info": "Cursor-authenticatie-informatie bijwerken", "register_process_error": "Registratieprocesfout: {error}", "setting_password": "Wachtwoord instellen", "manual_code_input": "Handmatige code-invoer", "manual_email_input": "Handmatige e-mailinvoer", "password": "Wachtwoord", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "Achternaam", "exit_signal": "Exit-signaal", "email_address": "E-mailadres", "config_created": "Configurat<PERSON>", "verification_failed": "Verificatie mislukt", "verification_error": "Verificatiefout: {error}", "config_option_added": "Configuratie<PERSON><PERSON> toegevoegd: {option}", "config_updated": "Configuratie bijgewerkt", "password_submitted": "Wachtwoord ingediend", "total_usage": "Totaal gebruik: {usage}", "setting_on_password": "Wachtwoord instellen", "getting_code": "Verificatiecode verkrijgen, opnieuw proberen in 60s", "using_browser": "Gebruik {browser} browser: {Path}", "could_not_track_processes": "Kon {browser} processen niet volgen: {error}", "try_install_browser": "Probeer de browser te installeren met uw pakket<PERSON>heerder", "tempmail_plus_verification_started": "<PERSON>t starten van TempMailplus -verificatieproces", "max_retries_reached": "Maximale opnieuw proberen pogingen bereikt. Registratie is mislukt.", "tempmail_plus_enabled": "TempMailplus is ingeschakeld", "browser_path_invalid": "{browser} pad is on<PERSON>dig, met behulp van standaardpad", "human_verify_error": "<PERSON>n niet controleren of de gebruiker menselijk is. Opnieuw proberen ...", "using_tempmail_plus": "TempMailplus gebruiken voor e -mailverificatie", "tracking_processes": "Tracking {count} {browser} processen", "tempmail_plus_epin_missing": "TempMailplus Epin is niet geconfigureerd", "tempmail_plus_verification_failed": "TempMailplus -verificatie is mislukt: {error}", "using_browser_profile": "Gebruik {browser} profiel van: {user_data_dir}", "tempmail_plus_verification_completed": "TempMailplus -verificatie met succes voltooid", "tempmail_plus_email_missing": "TempMailplus -e -mail is niet geconfigureerd", "tempmail_plus_config_missing": "TempMailplus -configuratie ontbreekt", "tempmail_plus_init_failed": "Kan TempMailplus niet initialiseren: {error}", "tempmail_plus_initialized": "Tempmailplus geïnitialiseerd met succes", "tempmail_plus_disabled": "TempMailplus is uitgeschakeld", "no_new_processes_detected": "<PERSON><PERSON> {browser} processen gedetecteerd om te volgen", "make_sure_browser_is_properly_installed": "<PERSON><PERSON>g ervoor dat {browser} correct is g<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "auth": {"title": "<PERSON><PERSON><PERSON>", "checking_auth": "Authenticatiebestand controleren", "auth_not_found": "Authenticatiebestand niet gevonden", "auth_file_error": "Authenticatiebestandfout: {error}", "reading_auth": "Authenticatiebestand lezen", "updating_auth": "Authenticatie-informatie bijwerken", "auth_updated": "Authenticatie-informatie succesvol bijgewerkt", "auth_update_failed": "Authenticatie-informatie bijwerken mislukt: {error}", "auth_file_created": "Authenticatiebestand aangemaakt", "auth_file_create_failed": "Authenticatiebestand aanmaken mislukt: {error}", "press_enter": "<PERSON>uk op Enter om door te gaan", "reset_machine_id": "Machine-ID resetten", "database_connection_closed": "Databaseverbinding gesloten", "database_updated_successfully": "Database succesvol bijgewerkt", "connected_to_database": "Verbonden met database", "updating_pair": "Sleutel-waarde paar bijwerken", "db_not_found": "Databasebestand niet gevonden op: {path}", "db_permission_error": "Kan geen toegang krijgen tot databasebestand. Controleer de rechten", "db_connection_error": "Verbinding met database mislukt: {error}"}, "control": {"generate_email": "Nieuw e-mailadres genereren", "blocked_domain": "Geblok<PERSON><PERSON>in", "select_domain": "<PERSON>ek<PERSON><PERSON> domein selecteren", "copy_email": "E-mailadres kopiëren", "enter_mailbox": "Mailbox betreden", "refresh_mailbox": "Mailbox vernieuwen", "check_verification": "Verificatiecode controleren", "verification_found": "Verificatiecode gevonden", "verification_not_found": "Geen verificatiecode gevonden", "browser_error": "Browserbesturingsfout: {error}", "navigation_error": "Navigatiefout: {error}", "email_copy_error": "E-mailkopieerfout: {error}", "mailbox_error": "Mailboxfout: {error}", "token_saved_to_file": "Token opgeslagen in cursor_tokens.txt", "navigate_to": "Navi<PERSON><PERSON> naar {url}", "generate_email_success": "E-mailadres succesvol gegenereerd", "select_email_domain": "E-maildomein selecteren", "select_email_domain_success": "E-maildomein succesvol geselecteerd", "get_email_name": "E-mailnaam verkri<PERSON>gen", "get_email_name_success": "E-mailnaam succesvol verkregen", "get_email_address": "E-mailadres verkrijgen", "get_email_address_success": "E-mailadres succesvol verkregen", "enter_mailbox_success": "Mailbox succesvol betreden", "found_verification_code": "Verificatiecode gevonden", "get_cursor_session_token": "Cursor-sessietoken verkrijgen", "get_cursor_session_token_success": "Cursor-sessietoken succesvol verkregen", "get_cursor_session_token_failed": "Cursor-sessietoken verkrijgen mislukt", "save_token_failed": "Token opslaan mislukt", "database_updated_successfully": "Database succesvol bijgewerkt", "database_connection_closed": "Databaseverbinding gesloten", "no_valid_verification_code": "Geen geldige verificatiecode"}, "email": {"starting_browser": "Browser starten", "visiting_site": "Bezoek mail domains", "create_success": "E-mail succesvol aangemaakt", "create_failed": "E-mail aanmaken mislukt", "create_error": "E-mail aanmaakfout: {error}", "refreshing": "E-mail vernieuwen", "refresh_success": "E-mail succesvol vernieuwd", "refresh_error": "E-mail vernieuwingsfout: {error}", "refresh_button_not_found": "Vernieuwknop niet gevonden", "verification_found": "Verificatie gevonden", "verification_not_found": "Verificatie niet gevonden", "verification_error": "Verificatiefout: {error}", "verification_code_found": "Verificatiecode gevonden", "verification_code_not_found": "Verificatiecode niet gevonden", "verification_code_error": "Verificatiecodefout: {error}", "address": "E-mailadres", "all_domains_blocked": "Alle domeinen g<PERSON>erd, service wisselen", "no_available_domains_after_filtering": "<PERSON><PERSON> besch<PERSON>e domeinen na filteren", "switching_service": "Wisselen naar {service} service", "domains_list_error": "Domeinenlijst verkrijgen mislukt: {error}", "failed_to_get_available_domains": "Verkrij<PERSON> van beschikbare domeinen mislukt", "domains_excluded": "Uitgesloten domeinen: {domains}", "failed_to_create_account": "Account a<PERSON><PERSON><PERSON> mislukt", "account_creation_error": "Account a<PERSON><PERSON><PERSON><PERSON><PERSON>: {error}", "domain_blocked": "<PERSON><PERSON>: {domain}", "no_display_found": "Geen display gevonden. Zorg ervoor dat X Server actief is.", "try_export_display": "Probeer: exporteren display =: 0", "try_install_chromium": "Probeer: sudo apt install chroom-browser", "blocked_domains": "Geblokkeerd<PERSON>inen: {domains}", "blocked_domains_loaded_timeout_error": "Geblokkeerde domeinen geladen time -outfout: {error}", "blocked_domains_loaded_success": "<PERSON><PERSON><PERSON>k<PERSON><PERSON><PERSON> met succes geladen", "extension_load_error": "Extension load error: {error}", "available_domains_loaded": "Beschikbare domeinen geladen: {count}", "blocked_domains_loaded_error": "Geblokkeerde domeinen geladen fout: {error}", "blocked_domains_loaded_timeout": "Geblokkeerde domeinen geladen time -out: {time -out} s", "make_sure_chrome_chromium_is_properly_installed": "Zorg ervoor dat chroom/chroom correct is g<PERSON><PERSON><PERSON><PERSON><PERSON>", "domains_filtered": "<PERSON><PERSON> gefilterd: {count}", "trying_to_create_email": "Proberen e -mail te maken: {e -mail}", "using_chrome_profile": "Gebruik van Chrome Profiel van: {user_data_dir}", "blocked_domains_loaded": "Geblokkeerde domeinen geladen: {count}"}, "update": {"title": "Cursor automatische update uitschakelen", "disable_success": "Automatische update is uitgeschakeld", "disable_failed": "Automatische update uitschakelen mislukt: {error}", "press_enter": "<PERSON>uk op Enter om door te gaan", "start_disable": "Automatische update uitschakelen starten", "killing_processes": "Processen verwijderen", "processes_killed": "<PERSON>en verwijderd", "removing_directory": "Map verwijderen", "directory_removed": "Map verwijderd", "creating_block_file": "Blokkeerbestand aanmaken", "block_file_created": "Blokkeerbestand aangemaakt", "clearing_update_yml": "Update.yml -bestand wissen", "update_yml_cleared": "update.yml -bestand gewist", "unsupported_os": "Niet -ondersteund OS: {System}", "block_file_already_locked": "blokbestand is al vergrendeld", "yml_already_locked_error": "update.yml -bestand al vergrendelde fout: {error}", "update_yml_not_found": "update.yml -bestand niet gevonden", "yml_locked_error": "update.yml -bestand vergrendelde fout: {error}", "remove_directory_failed": "Kan map niet verwijderen: {error}", "yml_already_locked": "Update.yml -bestand is al vergrendeld", "create_block_file_failed": "Kan een blokbestand niet maken: {error}", "block_file_locked_error": "Blokkeerbestandsfout: {error}", "block_file_already_locked_error": "Blokkeerbestand al vergrendelde fout: {error}", "directory_locked": "Directory is vergrendeld: {Path}", "clear_update_yml_failed": "Kan update.yml -bestand niet wissen: {error}", "yml_locked": "Update.yml -bestand is vergrendeld", "block_file_locked": "blokbestand is vergrendeld"}, "updater": {"checking": "Controleren op updates...", "new_version_available": "Er is een nieuwe versie beschikba<PERSON>! (Huidige versie: {current}, Laatste versie: {latest})", "updating": "Aan het bijwerken naar de nieuwste versie. Het programma zal automatisch herstart worden.", "up_to_date": "U gebruikt de nieuwste versie.", "check_failed": "Controle op updates mislukt: {error}", "continue_anyway": "<PERSON><PERSON><PERSON> met de huidige versie...", "update_confirm": "Wil je de nieuwste versie gebruiken? (Y/n)", "update_skipped": "Update overgeslagen.", "invalid_choice": "Ongeldige keuze. Voer 'Y' of 'n' in.", "development_version": "Ontwikkelversie {current} > {latest}", "changelog_title": "Wijzigingslogboek", "rate_limit_exceeded": "GitHub API -snelheidslimiet overschreden. Update check overslaan."}, "totally_reset": {"title": "Cursor volledig herstellen", "checking_config": "Configuratiebestand controleren", "config_not_found": "Configuratiebestand niet gevonden", "no_permission": "Kan geen toegang krijgen tot configuratiebestand. Control<PERSON> de rechten", "reading_config": "Huidige configuratie lezen", "creating_backup": "Configuratie-back-up a<PERSON><PERSON><PERSON>", "backup_exists": "Back-up bestand bestaat, back-up stap over<PERSON>lagen", "generating_new_machine_id": "Nieuwe machine-ID genereren", "saving_new_config": "Nieuwe configuratie opslaan als JSON", "success": "<PERSON>ursor succesvol hersteld", "error": "<PERSON><PERSON><PERSON> mislukt: {error}", "press_enter": "<PERSON>uk op Enter om door te gaan", "reset_machine_id": "Machine-ID resetten", "database_connection_closed": "Databaseverbinding gesloten", "database_updated_successfully": "Database succesvol bijgewerkt", "connected_to_database": "Verbonden met database", "updating_pair": "Sleutel-waarde paar bijwerken", "db_not_found": "Databasebestand niet gevonden op: {path}", "db_permission_error": "Kan geen toegang krijgen tot databasebestand. Controleer de rechten", "db_connection_error": "Verbinding met database mislukt: {error}", "feature_title": "Functiebeschrijving", "feature_1": "Compleet verwijderen van Cursor AI-instellingen en configuratie", "feature_2": "<PERSON><PERSON> cache<PERSON>, inclusief AI-geschiedenis en prompts", "feature_3": "Machine-ID resetten om de proefperiode te omzeilen", "feature_4": "Nieuwe willekeurige machine-ID maken", "feature_5": "Aangepaste extensies en voorkeuren verwijderen", "feature_6": "Proefperiode- en activatiegegevens resetten", "feature_7": "Diepe scan voor verborgen licentie- en proefperiodebestanden", "feature_8": "Beveili<PERSON><PERSON> niet-<PERSON><PERSON><PERSON>-bestanden en -toe<PERSON>en behouden", "feature_9": "Compatibel met Windows, macOS en Linux", "disclaimer_title": "Disclaimer", "disclaimer_1": "Deze tool verwijdert alle Cursor AI-instellingen,", "disclaimer_2": "configuratie en cachegegevens. Deze actie is niet ongedaan te maken.", "disclaimer_3": "Uw codebestanden **worden niet** beïnv<PERSON><PERSON>, de tool is bedoeld om", "disclaimer_4": "Alleen gericht op Cursor AI-editorbestanden en proefperiodecontrolemechanisme.", "disclaimer_5": "Andere systemtoepassingen worden niet beï<PERSON>v<PERSON>.", "disclaimer_6": "Na het uitvoeren van deze tool moet u Cursor AI opnieuw instellen.", "disclaimer_7": "U accepteer<PERSON> de risico's zelf.", "confirm_title": "Weet u zeker dat u wilt doorgaan?", "confirm_1": "Deze actie verwijdert alle Cursor AI-instellingen,", "confirm_2": "configuratie en cachegegevens. Deze actie is niet ongedaan te maken.", "confirm_3": "Uw codebestanden **worden niet** beïnv<PERSON><PERSON>, de tool is bedoeld om", "confirm_4": "Alleen gericht op Cursor AI-editorbestanden en proefperiodecontrolemechanisme.", "confirm_5": "Andere systemtoepassingen worden niet beï<PERSON>v<PERSON>.", "confirm_6": "Na het uitvoeren van deze tool moet u Cursor AI opnieuw instellen.", "confirm_7": "U accepteer<PERSON> de risico's zelf.", "invalid_choice": "Ongeldige keuze. Voer 'Y' of 'n' in.", "skipped_for_safety": "Uit veiligheidsoverwegingen overgeslagen (niet Cursor-gerelateerd): {path}", "deleted": "Verwijderd: {path}", "error_deleting": "<PERSON><PERSON><PERSON><PERSON><PERSON> van {path} mislukt: {error}", "not_found": "<PERSON>and niet gevonden: {path}", "resetting_machine_id": "Machine-ID resetten om proefperiode te omzeilen...", "created_machine_id": "Nieuwe machine-ID gemaakt: {path}", "error_creating_machine_id": "Machine-ID-bestand {path} aanmaken mislukt: {error}", "error_searching": "Fout bij het zoeken naar bestand {path}: {error}", "created_extended_trial_info": "Nieuwe uitgebreide proefperiode-informatie gemaakt: {path}", "error_creating_trial_info": "Fout bij het aanmaken van proefperiode-informatiebestand {path}: {error}", "resetting_cursor_ai_editor": "Cursor AI-editor resetten... W<PERSON>t even.", "reset_cancelled": "<PERSON><PERSON> g<PERSON>, geen wijzigingen aangebracht.", "windows_machine_id_modification_skipped": "Windows machine-ID-aanpassing overgeslagen: {error}", "linux_machine_id_modification_skipped": "Linux machine-id-aanpassing overgeslagen: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Opmerking: Compleet machine-ID-reset kan vereisen dat u als beheerder uitvoert", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Opmerking: <PERSON><PERSON>et systeem machine-ID-reset kan vereisen dat u sudo-rechten hebt", "windows_registry_instructions": "📝 Opmerking: Compleet machine-ID-reset kan vereisen dat u als beheerder uitvoert", "windows_registry_instructions_2": "   Run 'regedit' en zoek naar de sleutels 'Cursor' of 'CursorAI' in HKEY_CURRENT_USER\\Software\\ en verwijder ze.\n", "reset_log_1": "Cursor AI is volledig hersteld en heeft de proefperiode omzeild!", "reset_log_2": "Start het systeem opnieuw om de wijzigingen te activeren.", "reset_log_3": "U moet Cursor AI opnieuw installeren, er zou nu een nieuwe proefperiode moeten zijn.", "reset_log_4": "Voor de beste resultaten raden we aan ook:", "reset_log_5": "Nieuwe proefperiode registreren met een ander e-mailadres", "reset_log_6": "<PERSON><PERSON>, VPN gebruiken om IP-adres te wijzigen", "reset_log_7": "Voordat u naar de Cursor AI-website gaat, verwijdert u de cookies en cache van uw browser", "reset_log_8": "Als het nog steeds niet werkt, probeert u Cursor AI op een andere locatie te installeren", "reset_log_9": "Als u eventuele problemen on<PERSON>, stuurt u een probleem naar de Github Issue Tracker: https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Onverwachte fout: {error}", "report_issue": "Rapporteer dit probleem op de Github Issue Tracker: https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "<PERSON><PERSON><PERSON><PERSON><PERSON> heeft proces afgebroken, afsluiten...", "return_to_main_menu": "Terug naar hoofdmenu...", "process_interrupted": "Proces afgebroken, afsluiten...", "press_enter_to_return_to_main_menu": "Druk op Enter om terug te gaan naar het hoofdmenu...", "removing_known": "Het verwijderen van bekende proefperiode- en licentiebestanden...", "performing_deep_scan": "Een diepe scan wordt uitgevoerd om andere proefperiode- en licentiebestanden te vinden...", "found_additional_potential_license_trial_files": "<PERSON><PERSON> <PERSON>i<PERSON> {count} andere potentiële proefperiode- en licentiebestanden gevonden", "checking_for_electron_localstorage_files": "Controleren op Electron localStorage-bestanden...", "no_additional_license_trial_files_found_in_deep_scan": "<PERSON><PERSON> andere proefperiode- of licentiebestanden gevonden in diepe scan", "removing_electron_localstorage_files": "Het verwij<PERSON><PERSON> van Electron localStorage-bestanden...", "electron_localstorage_files_removed": "Electron localStorage-bestanden verwijderd", "electron_localstorage_files_removal_error": "Fout bij het verwijderen van Electron localStorage-bestanden: {error}", "removing_electron_localstorage_files_completed": "Electron localStorage-bestanden verwijderd", "warning_title": "WAARSCHUWING", "direct_advanced_navigation": "Directe navigatie proberen naar een geavanceerd tabblad", "delete_input_error": "Fout bij het vinden van invoer verwijderen: {error}", "delete_input_not_found_continuing": "<PERSON><PERSON><PERSON><PERSON><PERSON> bevestigingsinvoer niet gevo<PERSON>, maar probeer toch door te gaan", "advanced_tab_not_found": "Advanced Tab niet gevonden na meerdere pogingen", "advanced_tab_error": "Fout bij het vinden van een geavanceerd tabblad: {error}", "delete_input_not_found": "Verwijder bevestigingsinvoer niet gevonden na meerdere pogingen", "failed_to_delete_file": "File niet verwijderen: {Path}", "operation_cancelled": "Bewerking geannuleerd. Verlaten zonder wijzigingen aan te brengen.", "removed": "Verwijderd: {Path}", "warning_6": "U moet Cursor AI opnieuw instellen na het uitvoeren van deze tool.", "delete_input_retry": "<PERSON><PERSON><PERSON><PERSON><PERSON> invoer niet gevo<PERSON>, poging {poging}/{max_attempts}", "warning_4": "om alleen Cursor AI -editorbestanden en proefdetectiemechanismen te richten.", "cursor_reset_failed": "Cursor AI -editor <PERSON><PERSON> mislukt: {error}", "login_redirect_failed": "Aanmeldingsomleiding is mislukt, directe navigatie uitproberen ...", "warning_5": "Andere toepassingen op uw systeem worden niet beïn<PERSON>loed.", "failed_to_delete_file_or_directory": "File of Directory niet verwijderen: {Path}", "failed_to_delete_directory": "Kan map niet verwijderen: {Path}", "resetting_cursor": "Cursor AI -editor resetten ... Wacht alstublieft.", "cursor_reset_completed": "Cursor AI -editor is volledig gereset en proefdetectie omzeild!", "warning_3": "Uw codebestanden worden niet beïnvloed en de tool is ontworpen", "advanced_tab_retry": "Geavanceerd tabblad niet gevo<PERSON>, poging {poging}/{max_attempts}", "completed_in": "Voltooid in {time} seconden", "advanced_tab_clicked": "Klik op het tabblad Geavanceerd", "already_on_settings": "Al op de instellingenpagina", "found_danger_zone": "Gevonden gevarenzone -sectie", "delete_button_retry": "Verwijderen knop niet gevo<PERSON>, poging {poging}/{max_attempts}", "failed_to_remove": "Kan niet worden verwijderd: {pad}", "failed_to_reset_machine_guid": "Kan <PERSON><PERSON>t niet resetten", "deep_scanning": "Diep scan uitvoeren voor extra proef-/licentiebestanden", "delete_button_clicked": "Klik op de Account -knop Verwijderen", "warning_7": "Gebruik op eigen risico", "delete_button_not_found": "Verwijder Account -knop niet gevonden na meerdere pogingen", "delete_button_error": "Fout bij het vinden van verwijderknop: {error}", "warning_1": "Deze actie zal alle Cursor AI -instellingen verwijderen,", "warning_2": "Configuraties en cache -gegevens. Deze actie kan niet ongedaan worden gemaakt.", "navigating_to_settings": "Navigeren naar de instellingenpagina ...", "cursor_reset_cancelled": "Cursor AI -editor <PERSON><PERSON>. Verlaten zonder wijzigingen aan te brengen."}, "chrome_profile": {"title": "Chrome Profiel Selectie", "select_profile": "Selecteer een Chrome profiel om te gebruiken:", "profile_list": "Beschikbare profielen:", "default_profile": "Standaard Profiel", "profile": "<PERSON>iel {number}", "no_profiles": "Geen Chrome profielen gevonden", "error_loading": "Fout bij laden van Chrome profielen: {error}", "profile_selected": "Geselecteerd profiel: {profile}", "invalid_selection": "Ongeldige selectie. Probeer het opnieuw", "warning_chrome_close": "Waarschuwing: Dit zal alle actieve Chrome processen sluiten"}, "restore": {"title": "Machine-ID <PERSON><PERSON><PERSON> vanaf backup", "starting": "Herstelproces van machine-ID starten", "no_backups_found": "<PERSON><PERSON> backups g<PERSON><PERSON>", "available_backups": "<PERSON><PERSON><PERSON><PERSON><PERSON> backups", "select_backup": "Selecteer een backup om te herstellen", "to_cancel": "om te annuleren", "operation_cancelled": "Bewerking geannu<PERSON>rd", "invalid_selection": "Ongeldige selectie", "please_enter_number": "<PERSON><PERSON>r een geldig nummer in", "missing_id": "Ontbrekende ID: {id}", "read_backup_failed": "<PERSON><PERSON> van backupbestand mislukt: {error}", "current_file_not_found": "Huidig opslagbestand niet gevonden", "current_backup_created": "<PERSON><PERSON> van huidig opslagbestand gemaakt", "storage_updated": "Opslagbestand succesvol bijgewerkt", "update_failed": "Bijwerken van opslagbestand mislukt: {error}", "sqlite_not_found": "SQLite-database niet gevonden", "updating_sqlite": "SQLite-database bijwerken", "updating_pair": "Sleutel-waardepaar bijwerken", "sqlite_updated": "SQLite-database succesvol bijgewerkt", "sqlite_update_failed": "Bijwerken van SQLite-database mislukt: {error}", "machine_id_backup_created": "<PERSON>up van machineId-bestand gemaakt", "backup_creation_failed": "<PERSON><PERSON> van backup mislukt: {error}", "machine_id_updated": "MachineId-bestand succesvol bijgewerkt", "machine_id_update_failed": "Bijwerken van machineId-bestand mislukt: {error}", "updating_system_ids": "Systeem-ID's bijwerken", "system_ids_update_failed": "Bijwerken van systeem-ID's mislukt: {error}", "permission_denied": "Toegang geweigerd. Probeer als administrator uit te voeren", "windows_machine_guid_updated": "Windows machine-GUID succesvol bijgewerkt", "update_windows_machine_guid_failed": "Bijwerken van Windows machine-GUID mislukt: {error}", "windows_machine_id_updated": "Windows machine-ID succesvol bijgewerkt", "update_windows_machine_id_failed": "Bijwerken van Windows machine-ID mislukt: {error}", "sqm_client_key_not_found": "SQMClient registersleutel niet gevonden", "update_windows_system_ids_failed": "Bijwerken van Windows systeem-ID's mislukt: {error}", "macos_platform_uuid_updated": "macOS platform-UUID succesvol bijgewerkt", "failed_to_execute_plutil_command": "Uitvoeren van plutil opdracht mislukt", "update_macos_system_ids_failed": "Bijwerken van macOS systeem-ID's mislukt: {error}", "ids_to_restore": "<PERSON> herstellen machine-ID's", "confirm": "Weet u zeker dat u deze ID's wilt herstellen?", "success": "Machine-ID succesvol hersteld", "process_error": "Fout bij herstelproces: {error}", "press_enter": "<PERSON>uk op Enter om door te gaan"}, "oauth": {"no_chrome_profiles_found": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, met <PERSON><PERSON><PERSON>aard", "failed_to_delete_account": "Kan het account niet verwijderen: {error}", "starting_new_authentication_process": "Het starten van een nieuw authenticatieproces ...", "found_email": "E -mail gevonden: {e -mail}", "github_start": "GitHub start", "already_on_settings_page": "Al op de instellingenpagina!", "starting_github_authentication": "GitHub -authenticatie starten ...", "status_check_error": "Statuscontrolefout: {error}", "account_is_still_valid": "Account is nog steeds geldig (gebruik: {gebruik})", "authentication_timeout": "Verificatie time -out", "using_first_available_chrome_profile": "Gebruik van het eerste beschikbare Chrome -profiel: {profiel}", "usage_count": "Gebruik Count: {Usage}", "google_start": "Google Start", "no_compatible_browser_found": "Geen compatibele browser gevonden. Installeer Google Chrome of Chromium.", "authentication_successful_getting_account_info": "Authenticatie succesvol, accountinformatie krijgen ...", "found_chrome_at": "Chrome gevonden op: {Path}", "error_getting_user_data_directory": "Fout bij het verkrijgen van gebruikersgegevens: {error}", "error_finding_chrome_profile": "Fout bij het vinden van chroomprofiel, met standaard: {error}", "auth_update_success": "Verzeker Succes bijwerken", "authentication_successful": "Authenticatie succesvol - e -mail: {e -mail}", "authentication_failed": "Authenticatie mislukt: {error}", "warning_browser_close": "WAARSCHUWING: dit wordt alle running {browser} processen gesloten", "supported_browsers": "Ondersteunde browsers voor {platform}", "authentication_button_not_found": "Authenticatieknop niet gevonden", "starting_new_google_authentication": "Nieuwe Google -authenticatie starten ...", "waiting_for_authentication": "Wachten op authenticatie ...", "found_default_chrome_profile": "Standaard Chrome -profiel gevonden", "starting_browser": "Browser starten op: {path}", "could_not_check_usage_count": "Kon het aantal gebruik niet controleren: {error}", "token_extraction_error": "Token -extractiefout: {error}", "profile_selection_error": "Fout tijdens profielselectie: {error}", "warning_could_not_kill_existing_browser_processes": "Waarschuwing: kon bestaande browserprocessen niet doden: {error}", "browser_failed_to_start": "Browser kon niet beginnen: {error}", "starting_re_authentication_process": "<PERSON><PERSON><PERSON> met her-authenticatiep<PERSON>ces ...", "redirecting_to_authenticator_cursor_sh": "Omleidend naar authenticator.cursor.sh ...", "found_browser_data_directory": "BROWSER Data Directory gevonden: {Path}", "browser_not_found_trying_chrome": "<PERSON>n {browser} niet vinden, in plaats daarvan chroom proberen", "found_cookies": "Gevonden {count} cookies", "auth_update_failed": "AUTH -update is mislukt", "failed_to_delete_expired_account": "Kan de verlopen account niet verwijderen", "browser_failed_to_start_fallback": "Browser kon niet beginnen: {error}", "navigating_to_authentication_page": "Navigeren naar de authenticatiepagina ...", "browser_closed": "Browser gesloten", "initializing_browser_setup": "<PERSON><PERSON><PERSON> van browseropstelling ...", "failed_to_delete_account_or_re_authenticate": "Kan de account niet verwijderen of opnieuw authenticeren: {error}", "detected_platform": "Gedetecteerd platform: {platform}", "failed_to_extract_auth_info": "Verificatie info niet extraheren: {error}", "starting_google_authentication": "Google -authenticatie starten ...", "browser_failed": "Browser kon niet beginnen: {error}", "using_browser_profile": "Gebruik browserprofiel: {profiel}", "consider_running_without_sudo": "Overweeg om het script zonder sudo uit te voeren", "try_running_without_sudo_admin": "Pro<PERSON>r te rennen zonder sudo/beheerdersrechten", "page_changed_checking_auth": "<PERSON><PERSON><PERSON>, auth -controle ...", "running_as_root_warning": "Runnen als root wordt niet aanbevolen voor browserautomatisering", "please_select_your_google_account_to_continue": "Selecteer uw Google -account om door te gaan ...", "browser_setup_failed": "Browser -instelling is mislukt: {error}", "missing_authentication_data": "Ontbrekende authenticatiegegevens: {data}", "using_configured_browser_path": "Gebruik geconfigureerd {browser} pad: {Path}", "could_not_find_usage_count": "Kon het aantal gebruik niet vinden: {error}", "killing_browser_processes": "Doden {browser} processen ...", "account_has_reached_maximum_usage": "Account heeft maximaal gebruik bereikt, {verwijderen}", "browser_setup_completed": "Browser setup met succes voltooid", "could_not_find_email": "Kon geen e -mail vinden: {error}", "user_data_dir_not_found": "{Browser} Gebruikersgegevensdirectory die niet wordt gevonden op {Path}, zal in plaats daarvan Chrome proberen", "found_browser_user_data_dir": "Gevonden {browser} gebruikersgegevens map: {Path}", "invalid_authentication_type": "Ongeldig authenticatietype"}, "manual_auth": {"auth_type_selected": "Geselecteerd authenticatietype: {Type}", "proceed_prompt": "Doorgaan? (J/N):", "auth_type_github": "<PERSON><PERSON><PERSON>", "confirm_prompt": "Bevestig de volgende informatie:", "invalid_token": "Ongeldig token. Authenticatie afgebroken.", "continue_anyway": "B<PERSON>jf eigenlijk doorgaan? (J/N):", "token_verified": "To<PERSON> heeft met succes geverifieerd!", "error": "Fout: {error}", "auth_update_failed": "Verificatieinformatie niet bijwerken", "auth_type_auth0": "Auth_0 (standaard)", "auth_type_prompt": "Selecteer Authenticatietype:", "verifying_token": "Het verifi<PERSON>ren van de validiteit van token ...", "auth_updated_successfully": "Authenticatie -informatie met succes bijgewerkt!", "email_prompt": "Voer e -mail in (laat leeg voor willekeurige e -mail):", "token_prompt": "Voer uw cursor -token in (access_token/refresh_token):", "title": "Handmatige cursorauthenticatie", "token_verification_skipped": "Token verificatie overgeslagen (check_user_autorized.py niet gevonden)", "random_email_generated": "Willekeurige e -mail gegenereerd: {e -mail}", "token_required": "Token is vereist", "auth_type_google": "Google", "operation_cancelled": "<PERSON><PERSON> g<PERSON>", "token_verification_error": "Fout bij het verifi<PERSON><PERSON> van token: {error}", "updating_database": "Cursor authenticatiedatabase bijwerken ..."}, "account_delete": {"delete_input_not_found": "Verwijder bevestigingsinvoer niet gevonden na meerdere pogingen", "confirm_button_not_found": "Bevestig de knop niet gevonden na meerdere pogingen", "logging_in": "Inloggen met Google ...", "confirm_button_error": "Fout bij het vinden van bevestigen: {error}", "delete_button_clicked": "Klik op de Account -knop Verwijderen", "confirm_prompt": "Weet u zeker dat u verder wilt gaan? (J/N):", "delete_button_error": "Fout bij het vinden van verwijderknop: {error}", "cancelled": "Account<PERSON><PERSON><PERSON><PERSON> geannu<PERSON>rd.", "error": "Fout tijdens het verwijderen van accounts: {error}", "interrupted": "Accountver<PERSON>jderingsproces onderbroken door de gebruiker.", "delete_input_not_found_continuing": "<PERSON><PERSON><PERSON><PERSON><PERSON> bevestigingsinvoer niet gevo<PERSON>, maar probeer toch door te gaan", "advanced_tab_retry": "Geavanceerd tabblad niet gevo<PERSON>, poging {poging}/{max_attempts}", "waiting_for_auth": "Wachten op Google -authenticatie ...", "typed_delete": "Getypt \"verwijderen\" in bevestigingsvak", "trying_settings": "Proberen naar de pagina Instellingen te navigeren ...", "delete_input_retry": "<PERSON><PERSON><PERSON><PERSON><PERSON> invoer niet gevo<PERSON>, poging {poging}/{max_attempts}", "email_not_found": "E -mail niet gevonden: {error}", "delete_button_not_found": "Verwijder Account -knop niet gevonden na meerdere pogingen", "already_on_settings": "Al op de instellingenpagina", "failed": "Accountverwijderingsproces is mislukt of werd geannuleerd.", "warning": "Waarschuwing: dit zal uw cursoraccount permanent verwijderen. Deze actie kan niet ongedaan worden gemaakt.", "direct_advanced_navigation": "Directe navigatie proberen naar een geavanceerd tabblad", "advanced_tab_not_found": "Advanced Tab niet gevonden na meerdere pogingen", "auth_timeout": "Time -out van authenticatie, toch doorgaan ...", "select_google_account": "Selecteer uw Google -account ...", "google_button_not_found": "Google inlogknop niet gevonden", "found_danger_zone": "Gevonden gevarenzone -sectie", "account_deleted": "Account met succes verwijderd!", "advanced_tab_error": "Fout bij het vinden van een geavanceerd tabblad: {error}", "starting_process": "Beginnende accountverwijderingsproces ...", "delete_button_retry": "Verwijderen knop niet gevo<PERSON>, poging {poging}/{max_attempts}", "login_redirect_failed": "Aanmeldingsomleiding is mislukt, directe navigatie uitproberen ...", "unexpected_error": "Onverwachte fout: {error}", "login_successful": "Log succesvol in", "delete_input_error": "Fout bij het vinden van invoer verwijderen: {error}", "advanced_tab_clicked": "Klik op het tabblad Geavanceerd", "unexpected_page": "Onverwachte pagina na inloggen: {url}", "found_email": "E -mail gevonden: {e -mail}", "title": "Cursor Google Account Deletion Tool", "navigating_to_settings": "Navigeren naar de instellingenpagina ...", "success": "Uw cursoraccount is met succes verwijderd!", "confirm_button_retry": "Bevestig knop niet gevo<PERSON>, poging {poging}/{max_attempts}"}, "auth_check": {"token_length": "Tokenlengte: {lengte} tekens", "usage_response_status": "Gebruiksresponsstatus: {response}", "operation_cancelled": "Bewerking geannuleerd door gebruiker", "error_getting_token_from_db": "Fout om uit database te komen: {error}", "checking_usage_information": "Gebruiksinformatie controleren ...", "usage_response": "Gebruiksrespons: {response}", "authorization_failed": "Autorisatie is mislukt!", "authorization_successful": "Autorisatie succesvol!", "request_timeout": "<PERSON><PERSON><PERSON><PERSON> getimed uit", "check_error": "Foutcontrole -autorisatie: {error}", "connection_error": "Verbindingsfout", "invalid_token": "Ongeldig token", "check_usage_response": "Controleer gebruiksreactie: {response}", "enter_token": "<PERSON><PERSON>r uw cursor -token in:", "token_found_in_db": "Token gevonden in database", "user_unauthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON> is ongeautoriseerd", "checking_authorization": "Autorisatie controleren ...", "error_generating_checksum": "Fout genereren controlesom: {error}", "token_source": "Token uit de database of handmatig invoer? (D/M, standaard: D)", "unexpected_error": "Onverwachte fout: {error}", "user_authorized": "Gebruiker is geautoriseerd", "token_not_found_in_db": "Token niet gevonden in database", "jwt_token_warning": "Token lijkt in JWT -indeling te zijn, maar API -controle heeft een onverwachte statuscode geretourneerd. Het token kan geldig zijn, maar API -toegang is beperkt.", "unexpected_status_code": "Onverwachte statuscode: {code}", "getting_token_from_db": "Token uit de database ...", "cursor_acc_info_not_found": "cursor_acc_info.py niet gevonden"}, "token": {"refreshing": "Verfrissend token ...", "extraction_error": "Fout bij het extraheren van token: {error}", "no_access_token": "<PERSON><PERSON> toegang token als reactie", "invalid_response": "Ongeldige JSON -<PERSON><PERSON> Server", "connection_error": "Verbindingsfout om de server te vernieuwen", "unexpected_error": "Onverwachte fout tijdens tokenvernieuwing: {error}", "server_error": "Vernieuw de serverfout: http {status}", "refresh_success": "To<PERSON> met succes verfrist! Geldig voor {dagen} dagen (verloopt: {verlopen})", "request_timeout": "Verzoek om de server te vernieuwen", "refresh_failed": "To<PERSON> Vernieuwen is mislukt: {error}"}, "browser_profile": {"profile_selected": "Geselecteerd profiel: {profiel}", "default_profile": "Standaardprofiel", "no_profiles": "<PERSON><PERSON> {browser} pro<PERSON><PERSON> gevonden", "select_profile": "Selecteer {browser} profiel om te gebruiken:", "error_loading": "Fout laden {browser} profielen: {error}", "invalid_selection": "Ongeldige selectie. Probeer het opnieuw.", "title": "<PERSON><PERSON>", "profile": "<PERSON><PERSON> {nummer}", "profile_list": "Beschikbaar {browser} profielen:"}, "github_register": {"feature2": "Registreert een nieuw GitHub -account met willekeurige referenties.", "feature6": "Slaat alle referenties op een bestand op.", "starting_automation": "Automatisering starten ...", "feature1": "Genereert een tijdelijke e -mail met 1SecMail.", "title": "GitHub + Cursor AI Registratieautomatisering", "github_username": "GitHub -gebruikersnaam", "check_browser_windows_for_manual_intervention_or_try_again_later": "Controleer browservensters op handmatige interventie of probeer het later opnieuw.", "warning1": "Dit script automatiseert het maken van accounts, die GitHub/Cursor -servicevoorwaarden kan schenden.", "feature4": "<PERSON><PERSON> aan bij Cursor AI met <PERSON><PERSON><PERSON> -authenticatie.", "completed_successfully": "GitHub + Cursor -registratie is met succes voltooid!", "invalid_choice": "Ongeldige keuze. V<PERSON>r 'ja' of 'nee' in", "warning2": "Vereist internettoegang en administratieve privileges.", "registration_encountered_issues": "GitHub + Cursor -registratie ondervindt problemen.", "credentials_saved": "Deze referenties zijn opgeslagen in github_cursor_accounts.txt", "feature3": "Verifieert de GitHub -e -mail automatisch.", "github_password": "GitHub -wachtwoord", "features_header": "Functies", "feature5": "Reset de machine -ID om proefdetectie te omzeilen.", "warning4": "Gebruik verantwoord en op eigen risico.", "warning3": "<PERSON><PERSON> of aanvullende verificatie kan automatisering onderbreken.", "cancelled": "<PERSON><PERSON> g<PERSON>", "warnings_header": "Waarschuwingen", "program_terminated": "<PERSON><PERSON> be<PERSON>igd door gebruiker", "confirm": "Weet u zeker dat u verder wilt gaan?", "email_address": "E -mailadres"}, "account_info": {"subscription": "Abonnement", "failed_to_get_account_info": "Kan accountgege<PERSON> niet krijgen", "subscription_type": "Type abonnement", "pro": "Pro", "failed_to_get_account": "Kan accountgege<PERSON> niet krijgen", "config_not_found": "Configuratie niet gevonden.", "premium_usage": "Premium gebruik", "failed_to_get_subscription": "<PERSON><PERSON><PERSON> niet om abonnementsinformatie te krijgen", "basic_usage": "Basisgebruik", "premium": "<PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON>", "email_not_found": "E -mail niet gevonden", "title": "Accountinformatie", "inactive": "Inactief", "remaining_trial": "Resterende proef", "enterprise": "Onderneming", "usage_not_found": "Gebruik niet gevonden", "failed_to_get_usage": "Kan geen gebruiksinformatie krijgen", "lifetime_access_enabled": "Lifetime Access ingeschakeld", "days_remaining": "Resterende dagen", "failed_to_get_token": "Kan geen <PERSON> krijgen", "token": "<PERSON><PERSON>", "subscription_not_found": "Abonnementsinformatie niet gevonden", "days": "dagen", "team": "Team", "token_not_found": "Token niet gevonden", "active": "Actief", "email": "E -mail", "failed_to_get_email": "Niet -e -mailadres ontvangen", "pro_trial": "Pro -proef", "trial_remaining": "Resterende pro -proef", "usage": "Gebruik"}, "config": {"configuration": "Configuratie", "config_updated": "Config bijgewerkt", "file_owner": "Bestandseigenaar: {eigenaar}", "error_checking_linux_paths": "Fout bij het controleren van Linux -paden: {error}", "storage_file_is_empty": "Opslagbestand is leeg: {opslag_path}", "config_directory": "Configuratiemir<PERSON>y", "documents_path_not_found": "Documentenpad niet g<PERSON>, met be<PERSON><PERSON> van de huid<PERSON> map", "config_not_available": "Configuratie niet be<PERSON>", "neither_cursor_nor_cursor_directory_found": "Cursor noch cursor directory gevonden in {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Zorg ervoor dat de cursor is geïnstalleerd en is minstens één keer uitgevoerd", "using_temp_dir": "Tijdelijke map geb<PERSON>iken vanwege fout: {Path} (Error: {error})", "config_created": "Config gemaakt: {config_file}", "storage_file_not_found": "Opslagbestand niet gevonden: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "Het bestand kan worden be<PERSON><PERSON>, installeer de cursor opnieuw", "error_getting_file_stats": "Fout bij het verkrijgen van bestandsstatistieken: {error}", "enabled": "Ingeschakeld", "backup_created": "Back -up gemaakt: {Path}", "file_permissions": "Bestandsrechten: {machtigingen}", "config_setup_error": "Foutinstelling Config: {error}", "config_force_update_enabled": "Config File Force Update ingeschakeld, Forced Update uitvoeren", "config_removed": "Config -bestand verwijderd voor gedwongen update", "file_size": "Bestandsgrootte: {size} bytes", "error_reading_storage_file": "Fout lezen opslagbestand: {error}", "config_force_update_disabled": "Config File Force Update uitgeschakeld, overgeslagen update overslaan", "config_dir_created": "Configuratiemirectory gemaakt: {Path}", "config_option_added": "Configurat<PERSON>: {Option}", "file_group": "Bestandsgroep: {Group}", "and": "En", "backup_failed": "<PERSON>et back -up van configuratie: {error}", "force_update_failed": "Force Update Config mislukt: {error}", "storage_directory_not_found": "Opslagmap niet gevonden: {storage_dir}", "also_checked": "Ook gecontroleerd {pad}", "disabled": "Gehandicapt", "storage_file_found": "Opslagbestand gevonden: {opslag_path}", "try_running": "<PERSON><PERSON><PERSON> te runnen: {command}", "storage_file_is_valid_and_contains_data": "Opslagbestand is geldig en bevat gegevens", "permission_denied": "Toestemming geweigerd: {storage_path}"}, "bypass": {"found_product_json": "Gevonden product.json: {Path}", "starting": "Cursor -versie beginnen met bypass ...", "version_updated": "Versie bijgewerkt van {Old} naar {nieuw}", "menu_option": "Bypass cursorversie controle", "unsupported_os": "Niet -ondersteund besturingssysteem: {System}", "backup_created": "Back -up gemaakt: {Path}", "current_version": "Huidige versie: {versie}", "localappdata_not_found": "LocalAppData omgevingsvariabele niet gevonden", "no_write_permission": "<PERSON><PERSON> voor het bestand: {Path}", "write_failed": "Kan Product.json niet schrijven: {error}", "description": "Deze tool wijzigt Cursor's Product.json om versiebeperkingen te omzeilen", "bypass_failed": "Versie -bypass mislukt: {error}", "title": "Cursorversie Bypass Tool", "no_update_needed": "Geen update nodig. Huidige versie {versie} is al> = 0.46.0", "read_failed": "Faalde om product.json te lezen: {error}", "stack_trace": "St<PERSON><PERSON><PERSON><PERSON>", "product_json_not_found": "Product.json niet gevonden in gemeenschappelijke Linux -paden", "file_not_found": "<PERSON>and niet gevonden: {Path}"}, "bypass_token_limit": {"description": "Deze tool wijzigt het bestand Workbench.desktop.main.js om de tokenlimiet te omzeilen", "press_enter": "<PERSON>uk op Enter om door te gaan ...", "title": "Omzeilen token limiet tool"}, "tempmail": {"no_email": "<PERSON><PERSON> cursorverificatie -e -mail gevonden", "general_error": "Er is een fout opgetreden: {error}", "config_error": "Config -bestandsfout: {error}", "checking_email": "Controleren op cursorverificatie -e -mail ...", "extract_code_failed": "Extract Verificatiecode mislukt: {error}", "configured_email": "Geconfigureerd e -mail: {e -mail}", "no_code": "Kon geen verificatiecode krijgen", "email_found": "Cursor Verificatie -e -mail gevonden", "check_email_failed": "Controleer e -mail mislukt: {error}", "verification_code": "Verificatiecode: {code}"}}