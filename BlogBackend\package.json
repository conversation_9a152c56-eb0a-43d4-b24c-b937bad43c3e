{"name": "blogbackend", "version": "0.0.0", "private": true, "scripts": {"dev": "node ./scripts/pre-start.js && node-dev ./bin/www", "dev:skip-check": "node-dev ./bin/www", "start": "node ./scripts/pre-start.js && node ./bin/www", "start:skip-check": "node ./bin/www", "pre-start": "node ./scripts/pre-start.js", "db": "node ./scripts/db-manager.js", "setup-logs": "node ./scripts/setup-logs.js", "logs:clean": "node ./scripts/setup-logs.js && find ./logs -name '*.log' -mtime +30 -delete"}, "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.5.0", "express": "~4.16.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "http-errors": "~1.6.3", "jade": "~1.11.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "~1.9.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"node-dev": "^8.0.0"}}