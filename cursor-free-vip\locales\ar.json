{"menu": {"title": "الخيارات المتاحة", "exit": "خروج من البرنامج", "reset": "إعادة تعيين معرف الجهاز", "register": "تسجيل حسا<PERSON> <PERSON><PERSON> جديد", "register_google": "تسجيل باستخدام حساب جوجل", "register_github": "تسجيل باستخدام حساب <PERSON>", "register_manual": "تسجيل Cursor باستخدام بريد إلكتروني مخصص", "quit": "إغلاق تطبيق Cursor", "select_language": "تغيير اللغة", "select_chrome_profile": "اختيار ملف تعريف Chrome", "input_choice": "الرجاء إدخال اختيارك ({choices})", "invalid_choice": "اختيار غير صالح. الرجاء إدخال رقم من {choices}", "program_terminated": "تم إنهاء البرنامج بواسطة المستخدم", "error_occurred": "حد<PERSON> خطأ: {error}. ير<PERSON>ى المحاولة مرة أخرى", "press_enter": "اضغط Enter للخروج", "disable_auto_update": "تعطيل التحديث التلقائي لـ Cursor", "lifetime_access_enabled": "تم تفعيل الوصول مدى الحياة", "totally_reset": "إعادة تعيين Cursor بالكامل", "outdate": "قديم", "temp_github_register": "تسجيل GitHub مؤقت", "admin_required": "يجب تشغيل البرنامج كمسؤول عند التنفيذ.", "admin_required_continue": "المتابعة بدون صلاحيات المسؤول.", "coming_soon": "قريباً", "fixed_soon": "سيتم إصلاحه قريباً", "contribute": "المساهمة في المشروع", "config": "عرض الإعدادات", "delete_google_account": "حذ<PERSON> ح<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> المرتبط بجوجل", "continue_prompt": "المتابعة؟ (y/N): ", "operation_cancelled_by_user": "تم إلغاء العملية بواسطة المستخدم", "exiting": "جاري الخروج ……", "bypass_version_check": "تجاوز فحص إصدار Cursor", "check_user_authorized": "فحص صلاحية المستخدم", "bypass_token_limit": "تجاوز حد الرمز المميز (Token)", "restore_machine_id": "استعادة معرف الجهاز من النسخ الاحتياطي", "lang_invalid_choice": "اختيار غير صالح. الرجاء إدخال أحد الخيارات التالية: ({lang_choices})", "language_config_saved": "تم حفظ تكوين اللغة بنجاح", "manual_custom_auth": "مصادقة مخصصة يدوي"}, "languages": {"en": "الإنجليزية", "zh_cn": "الصينية المبسطة", "zh_tw": "الصينية التقليدية", "vi": "الفيتنامية", "nl": "الهولندية", "de": "الألمانية", "fr": "الفرنسية", "pt": "البرتغالية", "ru": "الروسية", "tr": "التركية", "bg": "البلغارية", "es": "الإسبانية", "ar": "العربية", "ja": "اليابانية", "it": "إيطالي"}, "quit_cursor": {"start": "ب<PERSON><PERSON> إغلاق Cursor", "no_process": "لا توجد عمليات Cursor قيد التشغيل", "terminating": "إنهاء العملية {pid}", "waiting": "في انتظار إنهاء العملية", "success": "تم إغلاق جميع عمليات Cursor", "timeout": "انتهت مهلة العملية: {pids}", "error": "حد<PERSON> خطأ: {error}"}, "reset": {"title": "أداة إعادة تعيين معرف جهاز Cursor", "checking": "جارٍ فحص ملف الإعدادات", "not_found": "لم يتم العثور على ملف الإعدادات", "no_permission": "لا يمكن قراءة أو كتابة ملف الإعدادات، يرجى التحقق من صلاحيات الملف", "reading": "جارٍ قراءة الإعدادات الحالية", "creating_backup": "جارٍ إنشاء نسخة احتياطية للإعدادات", "backup_exists": "النسخة الاحتياطية موجودة بالفعل، تخطي خطوة النسخ الاحتياطي", "generating": "جارٍ إنشاء معرف جهاز جديد", "saving_json": "جارٍ حفظ الإعدادات الجديدة في JSON", "success": "تم إعادة تعيين معرف الجهاز بنجاح", "new_id": "معرف الج<PERSON><PERSON><PERSON> الجديد", "permission_error": "خطأ في الصلاحيات: {error}", "run_as_admin": "يرجى محاولة تشغيل هذا البرنامج كمسؤول", "process_error": "خطأ في عملية الإعادة: {error}", "updating_sqlite": "جارٍ تحديث قاعدة بيانات SQLite", "updating_pair": "جارٍ تحديث زوج المفتاح-القيمة", "sqlite_success": "تم تحديث قاعدة بيانات SQLite بنجاح", "sqlite_error": "فشل تحديث قاعدة بيانات SQLite: {error}", "press_enter": "اضغط Enter للخروج", "unsupported_os": "نظام تشغيل غير مدعوم: {os}", "linux_path_not_found": "لم يتم العثور على مسار Linux", "updating_system_ids": "جارٍ تحديث معرفات النظام", "system_ids_updated": "تم تحديث معرفات النظام بنجاح", "system_ids_update_failed": "فشل تحديث معرفات النظام: {error}", "windows_guid_updated": "تم تحديث Windows GUID بنجاح", "windows_permission_denied": "تم رفض الصلاحية في Windows", "windows_guid_update_failed": "فشل تحديث Windows GUID", "macos_uuid_updated": "تم تحديث macOS UUID بنجاح", "plutil_command_failed": "فشل أمر plutil", "start_patching": "بدء تصحيح getMachineId", "macos_uuid_update_failed": "فشل تحديث macOS UUID", "current_version": "إصد<PERSON><PERSON> Cursor الحالي: {version}", "patch_completed": "تم تصحيح getMachineId بنجاح", "patch_failed": "فشل تصحيح getMachineId: {error}", "version_check_passed": "تم اجتياز فحص إصدار Cursor", "file_modified": "تم تعديل الملف", "version_less_than_0_45": "إصدار Cursor < 0.45.0، تخطي تصحيح getMachineId", "detecting_version": "جارٍ اكتشاف إصدا<PERSON> Cursor", "patching_getmachineid": "جارٍ تصحيح getMachineId", "version_greater_than_0_45": "إصدار Cursor >= 0.45.0، جارٍ تصحيح getMachineId", "permission_denied": "تم رفض الصلاحية: {error}", "backup_created": "تم إنشاء نسخة احتياطية", "update_success": "تم التحديث بنجاح", "update_failed": "فشل التحديث: {error}", "windows_machine_guid_updated": "تم تحديث Windows Machine GUID بنجاح", "reading_package_json": "جارٍ قراءة package.json {path}", "invalid_json_object": "كائن JSON غير صالح", "no_version_field": "لم يتم العثور على حقل الإصدار في package.json", "version_field_empty": "حقل الإصدار فارغ", "invalid_version_format": "تنسيق إصدار غير صالح: {version}", "found_version": "تم العثور على الإصدار: {version}", "version_parse_error": "خطأ في تحليل الإصدار: {error}", "package_not_found": "لم يتم العثور على package.json: {path}", "check_version_failed": "فشل فحص الإصدار: {error}", "stack_trace": "تتبع المكدس", "version_too_low": "إصدار Cursor منخفض جداً: {version} < 0.45.0", "no_write_permission": "لا توجد صلاحيات كتابة: {path}", "path_not_found": "لم يتم العثور على المسار: {path}", "modify_file_failed": "فشل تعديل الملف: {error}", "windows_machine_id_updated": "تم تحديث Windows Machine ID بنجاح", "update_windows_machine_id_failed": "فشل تحديث Windows Machine ID: {error}", "update_windows_machine_guid_failed": "فشل تحديث Windows Machine GUID: {error}", "file_not_found": "لم يتم العثور على الملف: {path}"}, "register": {"title": "أداة تسجيل Cursor", "start": "بدء عملية التسجيل...", "handling_turnstile": "جارٍ معالجة التحقق الأمني...", "retry_verification": "إعادة محاولة التحقق...", "detect_turnstile": "جارٍ التحقق من الأمان...", "verification_success": "تم التحقق الأمني بنجاح", "starting_browser": "جارٍ فتح المتصفح...", "form_success": "تم إرسال النموذج بنجاح", "browser_started": "تم فتح المتصفح بنجاح", "waiting_for_second_verification": "في انتظار التحقق عبر البريد الإلكتروني...", "waiting_for_verification_code": "في انتظار رمز التحقق...", "password_success": "تم تعيين كلمة المرور بنجاح", "password_error": "تعذر تعيين كلمة المرور: {error}. يرجى المحاولة مرة أخرى", "waiting_for_page_load": "جارٍ تحميل الصفحة...", "first_verification_passed": "تم اجتياز التحقق الأولي بنجاح", "mailbox": "تم الوصول إلى صندوق البريد بنجاح", "register_start": "بدء التسجيل", "form_submitted": "تم إرسال النموذج، بدء التحقق...", "filling_form": "تعبئة النموذج", "visiting_url": "جارٍ زيارة الرابط", "basic_info": "تم إرسال المعلومات الأساسية", "handle_turnstile": "معالجة Turnstile", "no_turnstile": "لم يتم اكتشاف Turnstile", "turnstile_passed": "تم اجتياز Turnstile", "verification_start": "بدء الحصول على رمز التحقق", "verification_timeout": "انتهت مهلة الحصول على رمز التحقق", "verification_not_found": "لم يتم العثور على رمز تحقق", "try_get_code": "محاولة | {attempt} الحصول على رمز التحقق | الوقت المتبقي: {time}ثانية", "get_account": "جارٍ الحصول على معلومات الحساب", "get_token": "الحصول على رمز جلسة Cursor", "token_success": "تم الحصول على الرمز بنجاح", "token_attempt": "محاولة | {attempt} مرات للحصول على الرمز | سيتم إعادة المحاولة بعد {time}ثانية", "token_max_attempts": "تم الوصول إلى الحد الأقصى للمحاولات ({max}) | فشل الحصول على الرمز", "token_failed": "فشل الحصول على الرمز: {error}", "account_error": "فشل الحصول على معلومات الحساب: {error}", "press_enter": "اضغط Enter للخروج", "browser_start": "بدء تشغيل المتصفح", "open_mailbox": "جارٍ فتح صندوق البريد", "email_error": "فشل الحصول على عنوان البريد الإلكتروني", "setup_error": "خطأ في إعداد البريد الإلكتروني: {error}", "start_getting_verification_code": "بدء الحصول على رمز التحقق، سيتم المحاولة خلال 60 ثانية", "get_verification_code_timeout": "انتهت مهلة الحصول على رمز التحقق", "get_verification_code_success": "تم الحصول على رمز التحقق بنجاح", "try_get_verification_code": "محاولة | {attempt} الحصول على رمز التحقق | الوقت المتبقي: {remaining_time}ثانية", "verification_code_filled": "تم تعبئة رمز التحقق", "login_success_and_jump_to_settings_page": "تم تسجيل الدخول بنجاح والانتقال إلى صفحة الإعدادات", "detect_login_page": "تم اكتشاف صفحة تسجيل الدخول، بدء التسجيل...", "cursor_registration_completed": "تم تسجيل Cursor بنجاح!", "set_password": "تعيين كلمة المرور", "basic_info_submitted": "تم إرسال المعلومات الأساسية", "cursor_auth_info_updated": "تم تحديث معلومات مصادقة Cursor", "cursor_auth_info_update_failed": "فشل تحديث معلومات مصادقة Cursor", "reset_machine_id": "إعادة تعيين معرف الجهاز", "account_info_saved": "تم حفظ معلومات الحساب", "save_account_info_failed": "فشل حفظ معلومات الحساب", "get_email_address": "الحصول على عنوان البريد الإلكتروني", "update_cursor_auth_info": "تحديث معلومات مصادقة Cursor", "register_process_error": "خطأ في عملية التسجيل: {error}", "setting_password": "جارٍ تعيين كلمة المرور", "manual_code_input": "إدخال الرمز يدوياً", "manual_email_input": "إدخال البريد الإلكتروني يدوياً", "password": "كلمة المرور", "first_name": "الاسم الأول", "last_name": "الاسم الأخير", "exit_signal": "إشارة خروج", "email_address": "عنوان البريد الإلكتروني", "config_created": "تم إنشاء الإعدادات", "verification_failed": "فشل التحقق", "verification_error": "خطأ في التحقق: {error}", "config_option_added": "تمت إضافة خيار الإعدادات: {option}", "config_updated": "تم تحديث الإعدادات", "password_submitted": "تم إرسال كلمة المرور", "total_usage": "إجمالي الاستخدام: {usage}", "setting_on_password": "جارٍ تعيين كلمة المرور", "getting_code": "جارٍ الحصول على رمز التحقق، سيتم المحاولة خلال 60 ثانية", "human_verify_error": "تعذر التحقق من أن المستخدم بشري. جارٍ إعادة المحاولة...", "max_retries_reached": "تم الوصول إلى الحد الأقصى للمحاولات. فشل التسجيل.", "browser_path_invalid": "مسار {browser} غير صالح، جارٍ استخدام المسار الافتراضي", "using_browser": "جارٍ استخدام متصفح {browser}: {path}", "using_browser_profile": "جارٍ استخدام ملف تعريف {browser} من: {user_data_dir}", "make_sure_browser_is_properly_installed": "تأكد من تثبيت {browser} بشكل صحيح", "try_install_browser": "حاول تثبيت المتصفح باستخدام مدير الحزم الخاص بك", "tracking_processes": "جارٍ تتبع {count} عمليات {browser}", "no_new_processes_detected": "لم يتم اكتشاف عمليات {browser} جديدة للتتبع", "could_not_track_processes": "تعذر تتبع عمليات {browser}: {error}", "tempmail_plus_verification_completed": "تم الانتهاء من التحقق من TempMailPlus بنجاح", "tempmail_plus_initialized": "تهيئة TempMailPlus بنجاح", "tempmail_plus_epin_missing": "لم يتم تكوين TempMailplus Epin", "using_tempmail_plus": "باستخدام TempMailPlus للتحقق من البريد الإلكتروني", "tempmail_plus_disabled": "يتم تعطيل TempMailPlus", "tempmail_plus_verification_failed": "فشل التحقق من TempMailPlus: {error}", "tempmail_plus_email_missing": "لم يتم تكوين البريد الإلكتروني tempmailplus", "tempmail_plus_enabled": "يتم تمكين TempMailPlus", "tempmail_plus_verification_started": "بدء عملية التحقق من TempMailPlus", "tempmail_plus_config_missing": "تكوين TempMailPlus مفقود", "tempmail_plus_init_failed": "فشل تهيئة TempMailPlus: {error}"}, "auth": {"title": "مدير مصادقة Cursor", "checking_auth": "جارٍ فحص ملف المصادقة", "auth_not_found": "لم يتم العثور على ملف المصادقة", "auth_file_error": "خطأ في ملف المصادقة: {error}", "reading_auth": "جارٍ قراءة ملف المصادقة", "updating_auth": "جارٍ تحديث معلومات المصادقة", "auth_updated": "تم تحديث معلومات المصادقة بنجاح", "auth_update_failed": "فشل تحديث معلومات المصادقة: {error}", "auth_file_created": "تم إنشاء ملف المصادقة", "auth_file_create_failed": "فشل إنشاء ملف المصادقة: {error}", "press_enter": "اضغط Enter للخروج", "reset_machine_id": "إعادة تعيين معرف الجهاز", "database_connection_closed": "تم إغلاق اتصال قاعدة البيانات", "database_updated_successfully": "تم تحديث قاعدة البيانات بنجاح", "connected_to_database": "تم الاتصال بقاعدة البيانات", "updating_pair": "جارٍ تحديث زوج المفتاح-القيمة", "db_not_found": "لم يتم العثور على ملف قاعدة البيانات في: {path}", "db_permission_error": "لا يمكن الوصول إلى ملف قاعدة البيانات. يرجى التحقق من الصلاحيات", "db_connection_error": "فشل الاتصال بقاعدة البيانات: {error}"}, "control": {"generate_email": "جارٍ إنشاء بريد إلكتروني جديد", "blocked_domain": "نطاق محظور", "select_domain": "جارٍ اختيار نطاق عشوائي", "copy_email": "جارٍ نسخ عنوان البريد الإلكتروني", "enter_mailbox": "جارٍ الدخول إلى صندوق البريد", "refresh_mailbox": "جارٍ تحديث صندوق البريد", "check_verification": "جارٍ التحقق من رمز التحقق", "verification_found": "تم العثور على رمز التحقق", "verification_not_found": "لم يتم العثور على رمز تحقق", "browser_error": "خطأ في التحكم بالمتصفح: {error}", "navigation_error": "خطأ في التنقل: {error}", "email_copy_error": "خطأ في نسخ البريد الإلكتروني: {error}", "mailbox_error": "خطأ في صندوق البريد: {error}", "token_saved_to_file": "تم حفظ الرمز في ملف cursor_tokens.txt", "navigate_to": "جارٍ الانتقال إلى {url}", "generate_email_success": "تم إنشاء البريد الإلكتروني بنجاح", "select_email_domain": "اختيار نطاق البريد الإلكتروني", "select_email_domain_success": "تم اختيار نطاق البريد الإلكتروني بنجاح", "get_email_name": "الحصول على اسم البريد الإلكتروني", "get_email_name_success": "تم الحصول على اسم البريد الإلكتروني بنجاح", "get_email_address": "الحصول على عنوان البريد الإلكتروني", "get_email_address_success": "تم الحصول على عنوان البريد الإلكتروني بنجاح", "enter_mailbox_success": "تم الدخول إلى صندوق البريد بنجاح", "found_verification_code": "تم العثور على رمز التحقق", "get_cursor_session_token": "الحصول على رمز جلسة Cursor", "get_cursor_session_token_success": "تم الحصول على رمز جلسة Cursor بنجاح", "get_cursor_session_token_failed": "فشل الحصول على رمز جلسة Cursor", "save_token_failed": "فشل حفظ الرمز", "database_updated_successfully": "تم تحديث قاعدة البيانات بنجاح", "database_connection_closed": "تم إغلاق اتصال قاعدة البيانات", "no_valid_verification_code": "لا يوجد رمز تحقق صالح"}, "email": {"starting_browser": "جارٍ بدء تشغيل المتصفح", "visiting_site": "جارٍ زيارة نطاقات البريد", "create_success": "تم إنشاء البريد الإلكتروني بنجاح", "create_failed": "فشل إنشاء البريد الإلكتروني", "create_error": "خطأ في إنشاء البريد الإلكتروني: {error}", "refreshing": "جارٍ تحديث البريد الإلكتروني", "refresh_success": "تم تحديث البريد الإلكتروني بنجاح", "refresh_error": "خطأ في تحديث البريد الإلكتروني: {error}", "refresh_button_not_found": "لم يتم العثور على زر التحديث", "verification_found": "تم العثور على التحقق", "verification_not_found": "لم يتم العثور على التحقق", "verification_error": "خطأ في التحقق: {error}", "verification_code_found": "تم العثور على رمز التحقق", "verification_code_not_found": "لم يتم العثور على رمز تحقق", "verification_code_error": "خطأ في رمز التحقق: {error}", "address": "عنوان البريد الإلكتروني", "all_domains_blocked": "جميع النطاقات محظورة، جارٍ التحويل إلى خدمة أخرى", "no_available_domains_after_filtering": "لا توجد نطاقات متاحة بعد التصفية", "switching_service": "جارٍ التحويل إلى خدمة {service}", "domains_list_error": "فشل الحصول على قائمة النطاقات: {error}", "failed_to_get_available_domains": "فشل الحصول على نطاقات متاحة", "domains_excluded": "النطاقات المستثناة: {domains}", "failed_to_create_account": "فشل إنشاء الحساب", "account_creation_error": "خطأ في إنشاء الحساب: {error}", "blocked_domains": "النطاقات المحظورة: {domains}", "blocked_domains_loaded": "تم تحميل النطاقات المحظورة: {count}", "blocked_domains_loaded_error": "خطأ في تحميل النطاقات المحظورة: {error}", "blocked_domains_loaded_success": "تم تحميل النطاقات المحظورة بنجاح", "blocked_domains_loaded_timeout": "انتهت مهلة تحميل النطاقات المحظورة: {timeout}ثانية", "blocked_domains_loaded_timeout_error": "خطأ في مهلة تحميل النطاقات المحظورة: {error}", "available_domains_loaded": "تم تحميل النطاقات المتاحة: {count}", "domains_filtered": "تم تصفية النطاقات: {count}", "trying_to_create_email": "جارٍ محاولة إنشاء بريد إلكتروني: {email}", "domain_blocked": "النطاق محظور: {domain}", "using_chrome_profile": "جارٍ استخدام ملف تعريف Chrome من: {user_data_dir}", "no_display_found": "لم يتم العثور على شاشة. تأكد من تشغيل خادم X.", "try_export_display": "حاول: export DISPLAY=:0", "extension_load_error": "خطأ في تحميل الامتداد: {error}", "make_sure_chrome_chromium_is_properly_installed": "تأكد من تثبيت Chrome/Chromium بشكل صحيح", "try_install_chromium": "حاول: sudo apt install chromium-browser"}, "update": {"title": "تعطيل التحديث التلقائي لـ Cursor", "disable_success": "تم تعطيل التحديث التلقائي بنجاح", "disable_failed": "فشل تعطيل التحديث التلقائي: {error}", "press_enter": "اضغط Enter للخروج", "start_disable": "بدء تعطيل التحديث التلقائي", "killing_processes": "جارٍ إنهاء العمليات", "processes_killed": "تم إنهاء العمليات", "removing_directory": "جارٍ إزالة الدليل", "directory_removed": "تمت إزالة الدليل", "creating_block_file": "جارٍ إنشاء ملف حظر", "block_file_created": "تم إنشاء ملف الحظر", "clearing_update_yml": "جارٍ مسح ملف update.yml", "update_yml_cleared": "تم مسح ملف update.yml", "update_yml_not_found": "لم يتم العثور على ملف update.yml", "clear_update_yml_failed": "فشل مسح ملف update.yml: {error}", "unsupported_os": "نظام تشغيل غير مدعوم: {system}", "remove_directory_failed": "فشل إزالة الدليل: {error}", "create_block_file_failed": "فشل إنشاء ملف الحظر: {error}", "directory_locked": "الدليل مقفل: {path}", "yml_locked": "ملف update.yml مقفل", "block_file_locked": "ملف الحظر مقفل", "yml_already_locked": "ملف update.yml مقفل بالفعل", "block_file_already_locked": "ملف الحظر مقفل بالفعل", "block_file_locked_error": "خطأ في قفل ملف الحظر: {error}", "yml_locked_error": "خطأ في قفل ملف update.yml: {error}", "block_file_already_locked_error": "خطأ في قفل ملف الحظر الموجود بالفعل: {error}", "yml_already_locked_error": "خطأ في قفل ملف update.yml الموجود بالفعل: {error}"}, "updater": {"checking": "جارٍ التحقق من التحديثات...", "new_version_available": "يتوفر إصدار جديد! (الحالي: {current}, الأحدث: {latest})", "updating": "جارٍ التحديث إلى أحدث إصدار. سيعيد البرنامج التشغيل تلقائياً.", "up_to_date": "أنت تستخدم أحدث إصدار.", "check_failed": "فشل التحقق من التحديثات: {error}", "continue_anyway": "المتابعة باستخدام الإصدار الحالي...", "update_confirm": "هل تريد التحديث إلى أحدث إصدار؟ (Y/n)", "update_skipped": "تخطي التحديث.", "invalid_choice": "اختيار غير صالح. الرجاء إدخال 'Y' أو 'n'.", "development_version": "إصدار التطوير {current} > {latest}", "changelog_title": "سجل التغييرات", "rate_limit_exceeded": "تم تجاوز حد معدل GitHub API. تخطي فحص التحديث."}, "totally_reset": {"title": "إعادة تعيين Cursor بالكامل", "checking_config": "جارٍ فحص ملف الإعدادات", "config_not_found": "لم يتم العثور على ملف الإعدادات", "no_permission": "لا يمكن قراءة أو كتابة ملف الإعدادات، يرجى التحقق من صلاحيات الملف", "reading_config": "جارٍ قراءة الإعدادات الحالية", "creating_backup": "جارٍ إنشاء نسخة احتياطية للإعدادات", "backup_exists": "النسخة الاحتياطية موجودة بالفعل، تخطي خطوة النسخ الاحتياطي", "generating_new_machine_id": "جارٍ إنشاء معرف جهاز جديد", "saving_new_config": "جارٍ حفظ الإعدادات الجديدة في JSON", "success": "تم إعادة تعيين Cursor بنجاح", "error": "فشل إعادة تعيين Cursor: {error}", "press_enter": "اضغط Enter للخروج", "reset_machine_id": "إعادة تعيين معرف الجهاز", "database_connection_closed": "تم إغلاق اتصال قاعدة البيانات", "database_updated_successfully": "تم تحديث قاعدة البيانات بنجاح", "connected_to_database": "تم الاتصال بقاعدة البيانات", "updating_pair": "جارٍ تحديث زوج المفتاح-القيمة", "db_not_found": "لم يتم العثور على ملف قاعدة البيانات في: {path}", "db_permission_error": "لا يمكن الوصول إلى ملف قاعدة البيانات. يرجى التحقق من الصلاحيات", "db_connection_error": "فشل الاتصال بقاعدة البيانات: {error}", "feature_title": "الميزات", "feature_1": "إزالة كاملة لإعدادات وتكوينات Cursor AI", "feature_2": "مسح جميع البيانات المخزنة مؤقتاً بما في ذلك سجل الذكاء الاصطناعي", "feature_3": "إعادة تعيين معرف الجهاز لتجاوز كشف الفترة التجريبية", "feature_4": "إنشاء معرفات أجهزة جديدة عشوائية", "feature_5": "إزالة الامتدادات المخصصة والتفضيلات", "feature_6": "إعادة تعيين معلومات الفترة التجريبية وبيانات التفعيل", "feature_7": "فحص عميق لملفات الرخصة والملفات المتعلقة بالفترة التجريبية المخفية", "feature_8": "الحفاظ على الملفات والتطبيقات غير المتعلقة بـ Cursor بأمان", "feature_9": "متوافق مع Windows وmacOS وLinux", "disclaimer_title": "تنبيه", "disclaimer_1": "ستقوم هذه الأداة بحذف جميع إعدادات Cursor AI،", "disclaimer_2": "والتكوينات والبيانات المخزنة مؤقتاً بشكل دائم. لا يمكن التراجع عن هذا الإجراء.", "disclaimer_3": "لن تتأثر ملفات الكود الخاصة بك، وقد صممت الأداة", "disclaimer_4": "للاستهداف فقط ملفات محرر Cursor AI وآليات كشف الفترة التجريبية.", "disclaimer_5": "لن تتأثر التطبيقات الأخرى على نظامك.", "disclaimer_6": "ستحتاج إلى إعداد Cursor AI مرة أخرى بعد تشغيل هذه الأداة.", "disclaimer_7": "استخدمها على مسؤوليتك الخاصة", "confirm_title": "هل أنت متأكد أنك تريد المتابعة؟", "confirm_1": "سيؤدي هذا الإجراء إلى حذف جميع إعدادات Cursor AI،", "confirm_2": "والتكوينات والبيانات المخزنة مؤقتاً. لا يمكن التراجع عن هذا الإجراء.", "confirm_3": "لن تتأثر ملفات الكود الخاصة بك، وقد صممت الأداة", "confirm_4": "للاستهداف فقط ملفات محرر Cursor AI وآليات كشف الفترة التجريبية.", "confirm_5": "لن تتأثر التطبيقات الأخرى على نظامك.", "confirm_6": "ستحتاج إلى إعداد Cursor AI مرة أخرى بعد تشغيل هذه الأداة.", "confirm_7": "استخدمها على مسؤوليتك الخاصة", "invalid_choice": "الرجاء إدخال 'Y' أو 'n'", "skipped_for_safety": "تم تخطيه لأسباب أمنية (غير متعلق بـ Cursor): {path}", "deleted": "تم الحذف: {path}", "error_deleting": "خطأ في حذف {path}: {error}", "not_found": "لم يتم العثور على الملف: {path}", "resetting_machine_id": "جارٍ إعادة تعيين معرفات الجهاز لتجاوز كشف الفترة التجريبية...", "created_machine_id": "تم إنشاء معرف جهاز جديد: {path}", "error_creating_machine_id": "خطأ في إنشاء ملف معرف الجهاز {path}: {error}", "error_searching": "خطأ في البحث عن الملفات في {path}: {error}", "created_extended_trial_info": "تم إنشاء معلومات الفترة التجريبية الممتدة: {path}", "error_creating_trial_info": "خطأ في إنشاء ملف معلومات الفترة التجريبية {path}: {error}", "resetting_cursor_ai_editor": "جارٍ إعادة تعيين محرر Cursor AI... يرجى الانتظار.", "reset_cancelled": "تم إلغاء الإعادة. الخروج دون إجراء أي تغييرات.", "windows_machine_id_modification_skipped": "تم تخطي تعديل معرف جهاز Windows: {error}", "linux_machine_id_modification_skipped": "تم تخطي تعديل machine-id في Linux: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "ملاحظة: قد تتطلب إعادة تعيين معرف الجهاز الكامل تشغيل البرنامج كمسؤول", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "ملاحظة: قد تتطلب إعادة تعيين معرف الجهاز الكامل صلاحيات sudo", "windows_registry_instructions": "📝 ملاحظة: للإعادة الكاملة على Windows، قد تحتاج أيضاً إلى تنظيف إدخالات التسجيل.", "windows_registry_instructions_2": "   قم بتشغيل 'regedit' وابحث عن المفاتيح التي تحتوي على 'Cursor' أو 'CursorAI' تحت HKEY_CURRENT_USER\\Software\\ واحذفها.\n", "reset_log_1": "تمت إعادة تعيين Cursor AI بالكامل وتجاوز كشف الفترة التجريبية!", "reset_log_2": "ير<PERSON>ى إعادة تشغيل النظام لتفعيل التغييرات.", "reset_log_3": "ستحتاج إلى إعادة تثبيت Cursor AI ويجب أن تحصل الآن على فترة تجريبية جديدة.", "reset_log_4": "للحصول على أفضل النتائج، ضع في الاعتبار أيضاً:", "reset_log_5": "استخدم عنوان بريد إلكتروني مختلف عند التسجيل للحصول على فترة تجريبية جديدة", "reset_log_6": "استخدم شبكة VPN لتغيير عنوان IP الخاص بك إذا كان ذلك متاحًا", "reset_log_7": "قم بمسح ملفات تعريف الارتباط وذاكرة التخزين المؤقت للمتصفح قبل زيارة موقع Cursor AI", "reset_log_8": "إذا استمرت المشكلات، حاول تثبيت Cursor AI في موقع مختلف", "reset_log_9": "إذا واجهتك أي مشكلات، انتقل إلى متتبع المشكلات على Github وقم بإنشاء مشكلة جديدة على https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "حد<PERSON> خطأ غير متوقع: {error}", "report_issue": "يرجى الإبلاغ عن هذه المشكلة على متتبع المشكلات في Github على https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "تمت مقاطعة العملية بواسطة المستخدم. جارٍ الخروج...", "return_to_main_menu": "جارٍ العودة إلى القائمة الرئيسية...", "process_interrupted": "تمت مقاطعة العملية. جارٍ الخروج...", "press_enter_to_return_to_main_menu": "اضغط على Enter للعودة إلى القائمة الرئيسية...", "removing_known": "جارٍ إزالة ملفات الفترة التجريبية/الترخيص المعروفة", "performing_deep_scan": "جارٍ إجراء فحص عميق للبحث عن ملفات ترخيص/تجريبية إضافية", "found_additional_potential_license_trial_files": "تم العثور على {count} ملفات محتملة إضافية للترخيص/الفترة التجريبية", "checking_for_electron_localstorage_files": "جارٍ التحقق من وجود ملفات التخزين المحلي لـ Electron", "no_additional_license_trial_files_found_in_deep_scan": "لم يتم العثور على ملفات ترخيص/تجريبية إضافية في الفحص العميق", "removing_electron_localstorage_files": "جارٍ إزالة ملفات التخزين المحلي لـ Electron", "electron_localstorage_files_removed": "تمت إزالة ملفات التخزين المحلي لـ Electron", "electron_localstorage_files_removal_error": "خطأ في إزالة ملفات التخزين المحلي لـ Electron: {error}", "removing_electron_localstorage_files_completed": "اكتملت إزالة ملفات التخزين المحلي لـ Electron", "warning_title": "تحذير", "warning_1": "هذا الإجراء سيحذف جميع إعدادات Cursor AI،", "warning_2": "والتكوينات والبيانات المخزنة مؤقتاً. لا يمكن التراجع عن هذا الإجراء.", "warning_3": "لن تتأثر ملفات الكود الخاصة بك، وقد صممت الأداة", "warning_4": "لاستهدا<PERSON> ملفات محرر Cursor AI وآليات اكتشاف الفترة التجريبية فقط.", "warning_5": "لن تتأثر التطبيقات الأخرى على نظامك.", "warning_6": "ستحتاج إلى إعداد Cursor AI مرة أخرى بعد تشغيل هذه الأداة.", "warning_7": "استخدم على مسؤوليتك الخاصة", "removed": "تمت الإزالة: {path}", "failed_to_reset_machine_guid": "فشل إعادة تعيين معرّف الجهاز", "failed_to_remove": "فشل في الإزالة: {path}", "failed_to_delete_file": "فشل في حذف الملف: {path}", "failed_to_delete_directory": "فشل في حذف المجلد: {path}", "failed_to_delete_file_or_directory": "فشل في حذف الملف أو المجلد: {path}", "deep_scanning": "جارٍ إجراء فحص عميق للبحث عن ملفات ترخيص/تجريبية إضافية", "resetting_cursor": "جارٍ إعادة تعيين محرر Cursor AI... يرجى الانتظار.", "completed_in": "اكتمل في {time} ثانية", "cursor_reset_completed": "تمت إعادة تعيين محرر Cursor AI بالكامل وتجاوز اكتشاف الفترة التجريبية!", "cursor_reset_failed": "فشلت إعادة تعيين محرر Cursor AI: {error}", "cursor_reset_cancelled": "تم إلغاء إعادة تعيين محرر Cursor AI. جارٍ الخروج دون إجراء أي تغييرات.", "operation_cancelled": "تم إلغاء العملية. جارٍ الخروج دون إجراء أي تغييرات.", "navigating_to_settings": "جارٍ الانتقال إلى صفحة الإعدادات...", "already_on_settings": "أنت بالفعل في صفحة الإعدادات", "login_redirect_failed": "فشلت إعادة توجيه تسجيل الدخول، جارٍ محاولة التنقل المباشر...", "advanced_tab_not_found": "لم يتم العثور على علامة التبويب المتقدمة بعد عدة محاولات", "advanced_tab_retry": "لم يتم العثور على علامة التبويب المتقدمة، المحاولة {attempt}/{max_attempts}", "advanced_tab_error": "خطأ في العثور على علامة التبويب المتقدمة: {error}", "advanced_tab_clicked": "تم النقر على علامة التبويب المتقدمة", "direct_advanced_navigation": "جارٍ محاولة التنقل المباشر إلى علامة التبويب المتقدمة", "delete_button_not_found": "لم يتم العثور على زر حذف الحساب بعد عدة محاولات", "delete_button_retry": "لم يتم العثور على زر الحذف، المحاولة {attempt}/{max_attempts}", "delete_button_error": "خطأ في العثور على زر الحذف: {error}", "delete_button_clicked": "تم النقر على زر حذف الحساب", "found_danger_zone": "تم العثور على قسم المنطقة الخطرة", "delete_input_not_found": "لم يتم العثور على حقل تأكيد الحذف بعد عدة محاولات", "delete_input_retry": "لم يتم العثور على حقل الحذف، المحاولة {attempt}/{max_attempts}", "delete_input_error": "خطأ في العثور على حقل الحذف: {error}", "delete_input_not_found_continuing": "لم يتم العثور على حقل تأكيد الحذف، جارٍ محاولة المتابعة على أي حال"}, "github_register": {"title": "أتمتة تسجيل GitHub و Cursor AI", "features_header": "الميزات", "feature1": "ينشئ بريدًا إلكترونيًا مؤقتًا باستخدام 1secmail.", "feature2": "يسجل حسا<PERSON> <PERSON><PERSON><PERSON><PERSON> جديد ببيانات اعتماد عشوائية.", "feature3": "يتحقق من بريد <PERSON> تلقائيًا.", "feature4": "يسجل الدخول إلى Cursor AI باستخدام مصادقة GitHub.", "feature5": "يعيد تعيين معرف الجهاز لتجاوز اكتشاف الفترة التجريبية.", "feature6": "يحفظ جميع بيانات الاعتماد في ملف.", "warnings_header": "تحذيرات", "warning1": "تقوم هذه الأداة بأتمتة إنشاء الحساب، مما قد ينتهك شروط خدمة GitHub/Cursor.", "warning2": "تتطلب وصولاً إلى الإنترنت وامتيازات المسؤول.", "warning3": "قد تتعارض CAPTCHA أو عمليات التحقق الإضافية مع الأتمتة.", "warning4": "استخدمها بمسؤولية وعلى مسؤوليتك الخاصة.", "confirm": "هل أنت متأكد أنك تريد المتابعة؟", "invalid_choice": "اختيار غير صالح. الرجاء إدخال 'yes' أو 'no'", "cancelled": "تم إلغاء العملية", "program_terminated": "تم إنهاء البرنامج بواسطة المستخدم", "starting_automation": "جارٍ بدء الأتمتة...", "github_username": "اسم مستخدم <PERSON>ub", "github_password": "كلمة مرور GitHub", "email_address": "عنوان البريد الإلكتروني", "credentials_saved": "تم حفظ بيانات الاعتماد هذه في ملف github_cursor_accounts.txt", "completed_successfully": "اكتمل تسجيل GitHub و Cursor بنجاح!", "registration_encountered_issues": "واجه تسجيل GitHub و Cursor مشكلات.", "check_browser_windows_for_manual_intervention_or_try_again_later": "تحقق من نوافذ المتصفح للتدخل اليدوي أو حاول مرة أخرى لاحقًا."}, "account_info": {"subscription": "الاشتراك", "trial_remaining": "المتبقي من الفترة التجريبية Pro", "days": "أيام", "subscription_not_found": "لم يتم العثور على معلومات الاشتراك", "email_not_found": "لم يتم العثور على البريد الإلكتروني", "failed_to_get_account": "فشل في الحصول على معلومات الحساب", "config_not_found": "لم يتم العثور على الإعدادات.", "failed_to_get_usage": "فشل في الحصول على معلومات الاستخدام", "failed_to_get_subscription": "فشل في الحصول على معلومات الاشتراك", "failed_to_get_email": "فشل في الحصول على عنوان البريد الإلكتروني", "failed_to_get_token": "فشل في الحصول على الرمز", "failed_to_get_account_info": "فشل في الحصول على معلومات الحساب", "title": "معلومات الحساب", "email": "الب<PERSON>يد الإلكتروني", "token": "الر<PERSON>ز", "usage": "الاستخدام", "subscription_type": "نوع الاشتراك", "remaining_trial": "الفترة التجريبية المتبقية", "days_remaining": "الأيام المتبقية", "premium": "مميز", "pro": "مح<PERSON><PERSON><PERSON>", "pro_trial": "تجريبي محترف", "team": "فريق", "enterprise": "مؤسسة", "free": "مجاني", "active": "نشط", "inactive": "غير نشط", "premium_usage": "استخدام مميز", "basic_usage": "استخدام أساسي", "usage_not_found": "لم يتم العثور على الاستخدام", "lifetime_access_enabled": "تم تمكين الوصول مدى الحياة", "token_not_found": "لم يتم العثور على الرمز"}, "config": {"config_not_available": "الإعدادات غير متاحة", "configuration": "الإعدادات", "enabled": "ممكّن", "disabled": "معطّل", "config_directory": "دليل الإعدادات", "neither_cursor_nor_cursor_directory_found": "لم يتم العثور على Cursor أو دليل Cursor في {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "يرجى التأكد من تثبيت Cursor وتشغيله مرة واحدة على الأقل", "storage_directory_not_found": "لم يتم العثور على دليل التخزين: {storage_dir}", "storage_file_found": "تم العثور على ملف التخزين: {storage_path}", "file_size": "حجم الملف: {size} بايت", "file_permissions": "صلاحيات الملف: {permissions}", "file_owner": "مالك الملف: {owner}", "file_group": "مجموعة الملف: {group}", "error_getting_file_stats": "خطأ في الحصول على إحصائيات الملف: {error}", "permission_denied": "تم رفض الإذن: {storage_path}", "try_running": "حاول تشغيل: {command}", "and": "و", "storage_file_is_empty": "ملف التخزين فارغ: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "قد يكون الملف تالفًا، يرجى إعادة تثبيت Cursor", "storage_file_not_found": "لم يتم العثور على ملف التخزين: {storage_path}", "error_checking_linux_paths": "خطأ في فحص مسارات Linux: {error}", "config_option_added": "تمت إضافة خيار الإعدادات: {option}", "config_updated": "تم تحديث الإعدادات", "config_created": "تم إنشاء الإعدادات: {config_file}", "config_setup_error": "خطأ في إعداد الإعدادات: {error}", "storage_file_is_valid_and_contains_data": "ملف التخزين صالح ويحتوي على بيانات", "error_reading_storage_file": "خطأ في قراءة ملف التخزين: {error}", "also_checked": "تم فحص {path} أيضًا", "backup_created": "تم إنشاء نسخة احتياطية: {path}", "config_removed": "تمت إزالة ملف الإعدادات للتحديث القسري", "backup_failed": "فشل نسخ الإعدادات احتياطيًا: {error}", "force_update_failed": "فشل تحديث الإعدادات القسري: {error}", "config_force_update_disabled": "تم تعطيل التحديث القسري لملف الإعدادات، جارٍ تخطي التحديث القسري", "config_force_update_enabled": "تم تمكين التحديث القسري لملف الإعدادات، جارٍ إجراء التحديث القسري", "documents_path_not_found": "لم يتم العثور على مسار المستندات، جارٍ استخدام الدليل الحالي", "config_dir_created": "تم إنشاء دليل الإعدادات: {path}", "using_temp_dir": "جارٍ استخدام دليل مؤقت بسبب خطأ: {path} (الخطأ: {error})"}, "oauth": {"authentication_button_not_found": "لم يتم العثور على زر المصادقة", "authentication_failed": "فشلت المصادقة: {error}", "found_cookies": "تم العثور على {count} من ملفات تعريف الارتباط", "token_extraction_error": "خطأ في استخراج الرمز: {error}", "authentication_successful": "تمت المصادقة بنجاح - الب<PERSON>يد الإلكتروني: {email}", "missing_authentication_data": "بيانات المصادقة مفقودة: {data}", "failed_to_delete_account": "فشل حذف الحساب: {error}", "invalid_authentication_type": "نوع المصادقة غير صالح", "auth_update_success": "تم تحديث المصادقة بنجاح", "browser_closed": "تم إغلاق المتصفح", "auth_update_failed": "فشل تحديث المصادقة", "google_start": "بدء Google", "github_start": "ب<PERSON><PERSON>", "usage_count": "عدد مرات الاستخدام: {usage}", "account_has_reached_maximum_usage": "وصل الحسا<PERSON> إلى الحد الأقصى للاستخدام، {deleting}", "starting_new_authentication_process": "جارٍ بدء عملية مصادقة جديدة...", "failed_to_delete_expired_account": "فشل حذف الح<PERSON><PERSON><PERSON> المنتهي", "could_not_check_usage_count": "تعذر التحقق من عدد مرات الاستخدام: {error}", "found_email": "تم العثور على البريد الإلكتروني: {email}", "could_not_find_email": "تعذر العثور على البريد الإلكتروني: {error}", "could_not_find_usage_count": "تعذر العثور على عدد مرات الاستخدام: {error}", "already_on_settings_page": "أنت بالفعل في صفحة الإعدادات!", "failed_to_extract_auth_info": "فشل استخراج معلومات المصادقة: {error}", "no_chrome_profiles_found": "لم يتم العثور على ملفات تعريف Chrome، جارٍ استخدام الملف الافتراضي", "found_default_chrome_profile": "تم العثور على ملف تعريف Ch<PERSON> الافتراضي", "using_first_available_chrome_profile": "جارٍ استخدام أول ملف تعريف Chrome متاح: {profile}", "error_finding_chrome_profile": "خطأ في العثور على ملف تعريف Chrome، جارٍ استخدام الملف الافتراضي: {error}", "initializing_browser_setup": "جارٍ تهيئة إعداد المتصفح...", "detected_platform": "تم اكتشاف النظام: {platform}", "running_as_root_warning": "التشغيل كمستخدم جذر غير مستحسن لأتمتة المتصفح", "consider_running_without_sudo": "فكر في تشغيل البرنامج بدون sudo", "no_compatible_browser_found": "لم يتم العثور على متصفح متوافق. يرجى تثبيت Google Chrome أو Chromium.", "supported_browsers": "المتصفحات المدعومة لـ {platform}", "using_browser_profile": "جارٍ استخدام ملف تعريف المتصفح: {profile}", "starting_browser": "جارٍ بدء المتصفح في: {path}", "browser_setup_completed": "تم إكمال إعداد المتصفح بنجاح", "browser_setup_failed": "فشل إعداد المتصفح: {error}", "try_running_without_sudo_admin": "حاول التشغيل بدون امتيازات sudo/المسؤول", "redirecting_to_authenticator_cursor_sh": "جارٍ إعادة التوجيه إلى authenticator.cursor.sh...", "starting_google_authentication": "جارٍ بدء مصادقة Google...", "starting_github_authentication": "جارٍ بدء مصادقة GitHub...", "waiting_for_authentication": "في انتظار المصادقة...", "page_changed_checking_auth": "تغيرت الصفحة، جارٍ التحقق من المصادقة...", "status_check_error": "خطأ في فحص الحالة: {error}", "authentication_timeout": "انتهت مهلة المصادقة", "account_is_still_valid": "الحساب لا يزال صالحًا (الاستخدام: {usage})", "starting_re_authentication_process": "جارٍ بدء عملية إعادة المصادقة...", "starting_new_google_authentication": "جارٍ بدء مصادقة Google جديدة...", "failed_to_delete_account_or_re_authenticate": "فشل حذف الحساب أو إعادة المصادقة: {error}", "navigating_to_authentication_page": "جارٍ الانتقال إلى صفحة المصادقة...", "please_select_your_google_account_to_continue": "يرجى اختيار حساب Google الخاص بك للمتابعة...", "found_browser_data_directory": "تم العثور على دليل بيانات المتصفح: {path}", "authentication_successful_getting_account_info": "تمت المصادقة بنجاح، جارٍ الحصول على معلومات الحساب...", "warning_could_not_kill_existing_browser_processes": "تحذير: تعذر إنهاء عمليات المتصفح الحالية: {error}", "browser_failed_to_start": "فشل بدء تشغيل المتصفح: {error}", "browser_failed": "فشل بدء تشغيل المتصفح: {error}", "browser_failed_to_start_fallback": "فشل بدء تشغيل المتصفح: {error}", "user_data_dir_not_found": "لم يتم العثور على دليل بيانات مستخدم {browser} في {path}، سيتم تجربة Chrome بدلاً من ذلك", "error_getting_user_data_directory": "خطأ في الحصول على دليل بيانات المستخدم: {error}", "warning_browser_close": "تحذير: سيؤدي هذا إلى إغلاق جميع عمليات {browser} قيد التشغيل", "killing_browser_processes": "جارٍ إنهاء عمليات {browser}...", "profile_selection_error": "خطأ أثناء اختيار الملف الشخصي: {error}", "using_configured_browser_path": "جارٍ استخدام مسار {browser} المُكوّن: {path}", "browser_not_found_trying_chrome": "تعذر العثور على {browser}، جارٍ تجربة Chrome بدلاً من ذلك", "found_chrome_at": "تم العثور على Chrome في: {path}", "found_browser_user_data_dir": "تم العثور على دليل بيانات مستخدم {browser}: {path}"}, "browser_profile": {"title": "اختيار ملف تعريف المتصفح", "select_profile": "اختر ملف تعريف {browser} للاستخدام:", "profile_list": "ملفات تعريف {browser} المتاحة:", "default_profile": "ملف التعريف الافتراضي", "profile": "ملف التعريف {number}", "no_profiles": "لم يتم العثور على ملفات تعريف {browser}", "error_loading": "خطأ في تحميل ملفات تعريف {browser}: {error}", "profile_selected": "ملف التعريف المحدد: {profile}", "invalid_selection": "اختيار غير صالح. يرجى المحاولة مرة أخرى."}, "account_delete": {"title": "أداة حذف حسا<PERSON> Cursor المرتبط بـ Google", "warning": "تحذير: سيؤدي هذا إلى حذف حسا<PERSON> Cursor الخاص بك بشكل دائم. لا يمكن التراجع عن هذا الإجراء.", "cancelled": "تم إلغاء حذف الحساب.", "starting_process": "جارٍ بدء عملية حذف الحساب...", "google_button_not_found": "لم يتم العثور على زر تسجيل الدخول بـ Google", "logging_in": "جارٍ تسجيل الدخول باستخدام Google...", "waiting_for_auth": "في انتظار مصادقة Google...", "login_successful": "تم تسجيل الدخول بنجاح", "unexpected_page": "صفحة غير متوقعة بعد تسجيل الدخول: {url}", "trying_settings": "جارٍ محاولة الانتقال إلى صفحة الإعدادات...", "select_google_account": "يرجى اختيار حساب Google الخاص بك...", "auth_timeout": "انتهت مهلة المصادقة، جارٍ المتابعة على أي حال...", "navigating_to_settings": "جارٍ الانتقال إلى صفحة الإعدادات...", "already_on_settings": "أنت بالفعل في صفحة الإعدادات", "login_redirect_failed": "فشلت إعادة توجيه تسجيل الدخول، جارٍ محاولة التنقل المباشر...", "advanced_tab_not_found": "لم يتم العثور على علامة التبويب المتقدمة بعد عدة محاولات", "advanced_tab_retry": "لم يتم العثور على علامة التبويب المتقدمة، المحاولة {attempt}/{max_attempts}", "advanced_tab_error": "خطأ في العثور على علامة التبويب المتقدمة: {error}", "advanced_tab_clicked": "تم النقر على علامة التبويب المتقدمة", "direct_advanced_navigation": "جارٍ محاولة التنقل المباشر إلى علامة التبويب المتقدمة", "delete_button_not_found": "لم يتم العثور على زر حذف الحساب بعد عدة محاولات", "delete_button_retry": "لم يتم العثور على زر الحذف، المحاولة {attempt}/{max_attempts}", "delete_button_error": "خطأ في العثور على زر الحذف: {error}", "delete_button_clicked": "تم النقر على زر حذف الحساب", "found_danger_zone": "تم العثور على قسم المنطقة الخطرة", "delete_input_not_found": "لم يتم العثور على حقل تأكيد الحذف بعد عدة محاولات", "delete_input_retry": "لم يتم العثور على حقل الحذف، المحاولة {attempt}/{max_attempts}", "delete_input_error": "خطأ في العثور على حقل الحذف: {error}", "delete_input_not_found_continuing": "لم يتم العثور على حقل تأكيد الحذف، جارٍ محاولة المتابعة على أي حال", "typed_delete": "تم كتابة \"Delete\" في مربع التأكيد", "confirm_button_not_found": "لم يتم العثور على زر التأكيد بعد عدة محاولات", "confirm_button_retry": "لم يتم العثور على زر التأكيد، المحاولة {attempt}/{max_attempts}", "confirm_button_error": "خطأ في العثور على زر التأكيد: {error}", "account_deleted": "تم حذف الحساب بنجاح!", "error": "<PERSON><PERSON><PERSON> أثناء حذف الحساب: {error}", "success": "تم حذف حسا<PERSON> <PERSON>ursor الخاص بك بنجاح!", "failed": "فشلت عملية حذف الحساب أو تم إلغاؤها.", "interrupted": "تمت مقاطعة عملية حذف الحساب بواسطة المستخدم.", "unexpected_error": "خطأ غير متوقع: {error}", "found_email": "تم العثور على البريد الإلكتروني: {email}", "email_not_found": "لم يتم العثور على البريد الإلكتروني: {error}", "confirm_prompt": "هل أنت متأكد أنك تريد المتابعة؟ (y/N): "}, "bypass": {"starting": "جارٍ بدء تجاوز إصدار Cursor...", "found_product_json": "تم العثور على product.json: {path}", "no_write_permission": "لا توجد صلاحية كتابة للملف: {path}", "read_failed": "فشلت قراءة product.json: {error}", "current_version": "الإصدار الحالي: {version}", "backup_created": "تم إنشاء نسخة احتياطية: {path}", "version_updated": "تم تحديث الإصدار من {old} إلى {new}", "write_failed": "فشلت كتابة product.json: {error}", "no_update_needed": "لا يلزم التحديث. الإصدار الحالي {version} بالفعل >= 0.46.0", "bypass_failed": "فشل تجاوز الإصدار: {error}", "stack_trace": "تتبع المكدس", "localappdata_not_found": "لم يتم العثور على متغير بيئة LOCALAPPDATA", "product_json_not_found": "لم يتم العثور على product.json في مسارات Linux الشائعة", "unsupported_os": "نظام تشغيل غير مدعوم: {system}", "file_not_found": "لم يتم العثور على الملف: {path}", "title": "أداة تجاوز إصدار Cursor", "description": "تقوم هذه الأداة بتعديل ملف product.json الخاص بـ Cursor لتجاوز قيود الإصدار", "menu_option": "تجاوز فحص إصدار Cursor"}, "auth_check": {"checking_authorization": "جارٍ التحقق من التصريح...", "token_source": "الحصول على الرمز من قاعدة البيانات أو إدخاله يدوياً؟ (d/m، الافتراضي: d)", "getting_token_from_db": "جارٍ الحصول على الرمز من قاعدة البيانات...", "token_found_in_db": "تم العثور على الرمز في قاعدة البيانات", "token_not_found_in_db": "لم يتم العثور على الرمز في قاعدة البيانات", "cursor_acc_info_not_found": "لم يتم العثور على cursor_acc_info.py", "error_getting_token_from_db": "خطأ في الحصول على الرمز من قاعدة البيانات: {error}", "enter_token": "أدخل رمز <PERSON> الخاص بك: ", "token_length": "طول الرمز: {length} حرفاً", "usage_response_status": "حالة استجابة الاستخدام: {response}", "unexpected_status_code": "رمز حالة غير متوقع: {code}", "jwt_token_warning": "يبدو أن الرمز بتنسيق JWT، لكن فحص API أعاد رمز حالة غير متوقع. قد يكون الرمز صالحاً ولكن الوصول إلى API مقيد.", "invalid_token": "رمز غير صالح", "user_authorized": "المستخدم مصرح له", "user_unauthorized": "المستخدم غير مصرح له", "request_timeout": "انتهت مهلة الطلب", "connection_error": "خطأ في الاتصال", "check_error": "خطأ في التحقق من التصريح: {error}", "authorization_successful": "تم التصريح بنجاح!", "authorization_failed": "فشل التصريح!", "operation_cancelled": "تم إلغاء العملية بواسطة المستخدم", "unexpected_error": "خطأ غير متوقع: {error}", "error_generating_checksum": "خطأ في إنشاء المجموع الاختباري: {error}", "checking_usage_information": "جارٍ التحقق من معلومات الاستخدام...", "check_usage_response": "استجابة فحص الاستخدام: {response}", "usage_response": "استجابة الاستخدام: {response}"}, "bypass_token_limit": {"title": "أداة تجاوز حد الرمز", "description": "تقوم هذه الأداة بتعديل ملف workbench.desktop.main.js لتجاوز حد الرمز", "press_enter": "اضغط Enter للمتابعة..."}, "token": {"refreshing": "جارٍ تحديث الرمز...", "refresh_success": "تم تحديث الرمز بنجاح! صالح لمدة {days} يوماً (تاريخ انتهاء الصلاحية: {expire})", "no_access_token": "لا يوجد رمز وصول في الاستجابة", "refresh_failed": "فشل تحديث الرمز: {error}", "invalid_response": "استجابة JSON غير صالحة من خادم التحديث", "server_error": "خطأ في خادم التحديث: HTTP {status}", "request_timeout": "انتهت مهلة طلب خادم التحديث", "connection_error": "خطأ في الاتصال بخادم التحديث", "unexpected_error": "خطأ غير متوقع أثناء تحديث الرمز: {error}", "extraction_error": "خطأ في استخراج الرمز: {error}"}, "restore": {"update_failed": "Failed to update storage file: {error}", "read_backup_failed": "Failed to read backup file: {error}", "please_enter_number": "Please enter a valid number", "no_backups_found": "No backup files found", "title": "Restore Machine ID from Backup", "sqlite_updated": "SQLite database updated successfully", "permission_denied": "Permission denied. Please try running as administrator", "sqlite_not_found": "SQLite database not found", "backup_creation_failed": "فشل في إنشاء نسخة احتياطية: {error}", "windows_machine_guid_updated": "تم تحديث GUID Machine Windows بنجاح", "select_backup": "حد<PERSON> الن<PERSON>خ الاحتياطي لاستعادة", "sqm_client_key_not_found": "لم يتم العثور على مفتاح تسجيل SQMClient", "machine_id_backup_created": "تم إنشاء نسخة احتياطية من ملف الجهاز", "system_ids_update_failed": "فشل في تحديث معرفات النظام: {error}", "current_backup_created": "تم إنشاء نسخة احتياطية من ملف التخزين الحالي", "update_windows_machine_guid_failed": "فشل في تحديث GUID MAVEN", "updating_pair": "تحديث زوج القيمة الرئيسية", "press_enter": "اضغط على Enter للمتابعة", "missing_id": "معرف مفقود: {id}", "current_file_not_found": "لم يتم العثور على ملف التخزين الحالي", "sqlite_update_failed": "فشل في تحديث قاعدة بيانات SQLite: {error}", "success": "تم استعادة معرف الجهاز بنجاح", "process_error": "استعادة خطأ العملية: {error}", "update_macos_system_ids_failed": "فشل في تحديث معرفات نظام MacOS: {error}", "machine_id_update_failed": "فشل في تحديث ملف الماكينة: {error}", "available_backups": "ملفات النسخ الاحتياطي المتاحة", "windows_machine_id_updated": "معرف جهاز Windows تم تحديثه بنجاح", "updating_sqlite": "تحديث قاعدة بيانات SQLite", "invalid_selection": "اختيار غير صالح", "update_windows_system_ids_failed": "فشل في تحديث معرفات نظام Windows: {error}", "macos_platform_uuid_updated": "تم تحديث منصة MacOS UUID بنجاح", "ids_to_restore": "معرفات الماكينة لاستعادة", "operation_cancelled": "تم إلغاء العملية", "machine_id_updated": "تم تحديث ملف الجهاز بنجاح", "update_windows_machine_id_failed": "فشل في تحديث معرف جهاز Windows: {error}", "storage_updated": "تم تحديث ملف التخزين بنجاح", "failed_to_execute_plutil_command": "فشل تنفيذ أمر بلوتيل", "updating_system_ids": "تحديث معرفات النظام", "starting": "بدء عملية استعادة معرف الجهاز", "confirm": "هل أنت متأكد من أنك تريد استعادة هذه المعرفات؟", "to_cancel": "للإلغاء"}, "manual_auth": {"auth_updated_successfully": "معلومات المصادقة تحديث بنجاح!", "proceed_prompt": "يتابع؟ (Y/N):", "auth_type_selected": "نوع المصادقة المحدد: {type}", "token_verification_skipped": "تم تخطي التحقق المميز (check_user_authorized.py غير موجود)", "auth_type_google": "جوجل", "token_required": "الرمز المميز مطلوب", "continue_anyway": "تواصل على أي حال؟ (Y/N):", "verifying_token": "التحقق من صحة الرمز المميز ...", "auth_type_github": "جي<PERSON><PERSON>", "error": "خطأ: {error}", "random_email_generated": "تم إنشاء بريد إلكتروني عشوائي: {البريد الإلكتروني}", "auth_type_prompt": "حدد نوع المصادقة:", "operation_cancelled": "تم إلغاء العملية", "auth_type_auth0": "Auth_0 (افتراضي)", "token_verification_error": "خطأ في التحقق من الرمز المميز: {error}", "email_prompt": "أدخل البريد الإلكتروني (اترك فارغًا للبريد الإلكتروني العشوائي):", "token_verified": "تم التحقق من الرمز المميز بنجاح!", "token_prompt": "أدخل رمز المؤشر الخاص بك (Access_Token/Refresh_token):", "confirm_prompt": "يرجى تأكيد المعلومات التالية:", "invalid_token": "رمز غير صالح. مصادقة أجهض.", "updating_database": "تحديث قاعدة بيانات مصادقة المؤشر ...", "title": "مصادقة المؤشر اليدوي", "auth_update_failed": "فشل في تحديث معلومات المصادقة"}, "tempmail": {"config_error": "خطأ في ملف التكوين: {error}", "no_email": "لم يتم العثور على بريد إلكتروني للتحقق من المؤشر", "general_error": "حد<PERSON> خطأ: {error}", "checking_email": "التحقق من البريد الإلكتروني للتحقق من المؤشر ...", "extract_code_failed": "فشل استخراج رمز التحقق: {error}", "configured_email": "البريد الإلكتروني المكون: {البريد الإلكتروني}", "check_email_failed": "فشل التحقق من البريد الإلكتروني: {error}", "no_code": "لا يمكن الحصول على رمز التحقق", "email_found": "تم العثور على البريد الإلكتروني للتحقق من المؤشر", "verification_code": "رمز التحقق: {code}"}}