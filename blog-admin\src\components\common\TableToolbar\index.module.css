.toolbar {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.toolbarContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 48px;
}

.toolbarLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title {
  margin: 0 !important;
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.5;
}

.selectedInfo {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  font-size: 12px;
  color: #0958d9;
}

.selectedCount {
  font-weight: 600;
  margin: 0 4px;
}

.toolbarRight {
  display: flex;
  align-items: center;
}

.primaryButton {
  height: 40px;
  border-radius: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.primaryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.primaryButton:hover::before {
  left: 100%;
}

.iconButton {
  height: 40px;
  width: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.iconButton:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbarContent {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbarLeft {
    justify-content: center;
  }

  .toolbarRight {
    justify-content: center;
  }

  .title {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .toolbarRight :global(.ant-space) {
    width: 100%;
    justify-content: center;
  }

  .primaryButton {
    flex: 1;
    max-width: 120px;
  }
}
