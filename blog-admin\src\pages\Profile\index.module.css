.card {
  border-radius: 18px;
  box-shadow: 0 4px 32px rgba(160, 140, 209, 0.15);
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  transition:
    box-shadow 0.3s,
    background 0.3s;
  margin-bottom: 32px;
  animation: card-pop 0.8s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.dark .card {
  background: rgba(40, 40, 60, 0.98);
  color: #fff;
  box-shadow: 0 4px 32px rgba(80, 60, 160, 0.25);
}

.profileBg {
  min-height: 100vh;
  background: linear-gradient(135deg, #f3e7fa 0%, #e3eeff 100%);
  transition: background 0.5s;
  position: relative;
}

.dark {
  background: linear-gradient(135deg, #232526 0%, #414345 100%);
}

.waveBg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 90px;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-bottom-left-radius: 60% 30px;
  border-bottom-right-radius: 60% 30px;
  z-index: 1;
  animation: waveMove 6s infinite linear alternate;
  opacity: 0.8;
}

@keyframes waveMove {
  0% {
    transform: skewY(-2deg) scaleX(1);
  }
  100% {
    transform: skewY(2deg) scaleX(1.04);
  }
}

.avatarGlow {
  animation: avatar-glow 2s infinite alternate;
  box-shadow:
    0 0 16px 4px #a18cd1,
    0 0 32px 8px #fbc2eb44;
}

@keyframes avatar-glow {
  0% {
    box-shadow:
      0 0 16px 4px #a18cd1,
      0 0 32px 8px #fbc2eb44;
  }
  100% {
    box-shadow:
      0 0 32px 8px #fbc2eb,
      0 0 48px 16px #a18cd144;
  }
}

.badgeTag {
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}
.badgeTag:hover {
  transform: scale(1.15) rotate(-6deg);
  box-shadow: 0 4px 16px #a18cd1aa;
}

@keyframes card-pop {
  0% {
    transform: scale(0.92) translateY(40px);
    opacity: 0;
  }
  80% {
    transform: scale(1.04) translateY(-8px);
    opacity: 1;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.dark .ant-descriptions-item-label {
  color: #bdbddd !important;
}
.dark .ant-descriptions-item-content {
  color: #fff !important;
}
.dark .ant-input {
  background: #23243a !important;
  color: #fff !important;
  border-color: #444466 !important;
}
.dark .ant-input:focus {
  border-color: #a18cd1 !important;
  box-shadow: 0 0 0 2px #a18cd144;
}
.dark .ant-tag-purple {
  background: #3a2e5a !important;
  color: #fbc2eb !important;
  border-color: #a18cd1 !important;
}
.dark .ant-card {
  background: #23243a !important;
  color: #fff !important;
}
.dark .ant-modal-content {
  background: #23243a !important;
  color: #fff !important;
}
.dark .ant-modal-title {
  color: #fbc2eb !important;
}

/* 强制暗色模式下所有文字、按钮、输入、标签、弹窗等高对比度显示 */
.dark,
.dark * {
  color: #f6f6fa !important;
  text-shadow: 0 1px 2px #2228;
}
.dark .ant-descriptions-item-label,
.dark .ant-descriptions-item-content,
.dark .ant-modal-title,
.dark .ant-btn,
.dark .ant-btn > span,
.dark .ant-input,
.dark .ant-tag,
.dark .ant-card,
.dark .ant-modal-content {
  color: #f6f6fa !important;
}
.dark .ant-btn-primary {
  background: #a18cd1 !important;
  border-color: #a18cd1 !important;
  color: #fff !important;
}
.dark .ant-btn-default {
  background: #23243a !important;
  border-color: #444466 !important;
  color: #f6f6fa !important;
}
.dark .ant-input,
.dark .ant-input:focus {
  background: #23243a !important;
  color: #f6f6fa !important;
  border-color: #a18cd1 !important;
}
.dark .ant-tag-purple {
  background: #3a2e5a !important;
  color: #fbc2eb !important;
  border-color: #a18cd1 !important;
}
.dark .ant-card {
  background: #23243a !important;
  color: #f6f6fa !important;
}
.dark .ant-modal-content {
  background: #23243a !important;
  color: #f6f6fa !important;
}
.dark .ant-modal-title {
  color: #fbc2eb !important;
}

.dark .ant-tag,
.dark .ant-tag > * {
  color: #fff !important;
  fill: #fff !important;
  font-weight: bold !important;
  text-shadow:
    0 0 6px #a18cd1,
    0 0 2px #000a;
}
.dark .ant-tag .anticon {
  color: #ffe066 !important;
  fill: #ffe066 !important;
  filter: drop-shadow(0 0 6px #a18cd1) drop-shadow(0 0 2px #fff);
}
