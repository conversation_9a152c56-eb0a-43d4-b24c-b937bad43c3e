.searchCard {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.searchCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.searchCard:hover {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.searchCard:hover::before {
  opacity: 1;
}

.cardTitle {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1a202c;
  font-size: 15px;
}

.titleIcon {
  margin-right: 10px;
  color: #667eea;
  font-size: 16px;
  padding: 4px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 6px;
}

.collapseBtn {
  padding: 4px 12px;
  font-size: 12px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.collapseBtn:hover {
  color: #fff;
  background: #667eea;
  border-color: #667eea;
}

.searchContent {
  transition: all 0.3s ease;
  overflow: hidden;
}

.searchContent.collapsed {
  max-height: 0;
  padding: 0;
  margin: 0;
}

.searchForm {
  width: 100%;
}

.formFields {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: flex-end;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formFields {
    flex-direction: column;
    gap: 12px;
  }

  .formFields :global(.ant-form-item) {
    margin-bottom: 0;
    width: 100%;
  }

  .formFields :global(.ant-form-item-control-input) {
    width: 100%;
  }

  .formActions {
    justify-content: center;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .formActions {
    flex-direction: column;
    gap: 8px;
  }

  .formActions :global(.ant-btn) {
    width: 100%;
  }
}
