{"menu": {"title": "Available Options", "exit": "Exit Program", "reset": "Reset Machine ID", "register": "Register New Cursor Account", "register_google": "Register with Self Google Account", "register_github": "Register with Self GitHub Account", "register_manual": "Register Cursor with Custom Email", "quit": "Close Cursor Application", "select_language": "Change Language", "select_chrome_profile": "Select Chrome Profile", "input_choice": "Please enter your choice ({choices})", "invalid_choice": "Invalid selection. Please enter a number from {choices}", "program_terminated": "Program was terminated by user", "error_occurred": "An error occurred: {error}. Please try again", "press_enter": "Press Enter to Exit", "disable_auto_update": "Disable Cursor Auto-Update", "lifetime_access_enabled": "LIFETIME ACCESS ENABLED", "totally_reset": "Totally Reset <PERSON>ursor", "outdate": "Outdated", "temp_github_register": "Temporary GitHub Register", "admin_required": "Running as executable, administrator privileges required.", "admin_required_continue": "Continuing without administrator privileges.", "coming_soon": "Coming Soon", "fixed_soon": "Fixed Soon", "contribute": "Contribute to the Project", "config": "Show Config", "delete_google_account": "Delete Cursor Google Account", "continue_prompt": "Continue? (y/N): ", "operation_cancelled_by_user": "Operation cancelled by user", "exiting": "Exiting ……", "bypass_version_check": "Bypass Cursor Version Check", "check_user_authorized": "Check User Authorized", "bypass_token_limit": "Bypass Token Limit", "language_config_saved": "Language configuration saved successfully", "lang_invalid_choice": "Invalid choice. Please enter one of the following options: ({lang_choices})", "restore_machine_id": "Restore Machine ID from Backup", "manual_custom_auth": "Manual Custom Auth"}, "languages": {"ar": "Arabic", "en": "English", "zh_cn": "简体中文", "zh_tw": "繁體中文", "vi": "Vietnamese", "nl": "Dutch", "de": "German", "fr": "French", "pt": "Portuguese", "ru": "Russian", "tr": "Turkish", "bg": "Bulgarian", "es": "Spanish", "ja": "Japanese", "it": "Italian"}, "quit_cursor": {"start": "Start Quitting Cursor", "no_process": "No Running Cursor Process", "terminating": "Terminating Process {pid}", "waiting": "Waiting for Process to Exit", "success": "All Cursor Processes Closed", "timeout": "Process Timeout: {pids}", "error": "Error Occurred: {error}"}, "reset": {"title": "Cursor Machine ID Reset Tool", "checking": "Checking Config File", "not_found": "Config File Not Found", "no_permission": "Cannot Read or Write Config File, Please Check File Permissions", "reading": "Reading Current Config", "creating_backup": "Creating Config <PERSON>up", "backup_exists": "Backup File Already Exists, Skipping Backup Step", "generating": "Generating New Machine ID", "saving_json": "Saving New Config to JSON", "success": "Machine ID Reset Successfully", "new_id": "New Machine ID", "permission_error": "Permission Error: {error}", "run_as_admin": "Please Try Running This Program as Administrator", "process_error": "Reset Process Error: {error}", "updating_sqlite": "Updating SQLite Database", "updating_pair": "Updating Key-Value Pair", "sqlite_success": "SQLite Database Updated Successfully", "sqlite_error": "SQLite Database Update Failed: {error}", "press_enter": "Press Enter to Exit", "unsupported_os": "Unsupported OS: {os}", "linux_path_not_found": "Linux Path Not Found", "updating_system_ids": "Updating System IDs", "system_ids_updated": "System IDs Updated Successfully", "system_ids_update_failed": "System IDs Update Failed: {error}", "windows_guid_updated": "Windows GUID Updated Successfully", "windows_permission_denied": "Windows Permission Denied", "windows_guid_update_failed": "Windows GUID Update Failed", "macos_uuid_updated": "macOS UUID Updated Successfully", "plutil_command_failed": "plutil Command Failed", "start_patching": "Starting Patching getMachineId", "macos_uuid_update_failed": "macOS UUID Update Failed", "current_version": "Current Cursor Version: {version}", "patch_completed": "Patching getMachineId Completed", "patch_failed": "Patching getMachineId Failed: {error}", "version_check_passed": "Cursor Version Check Passed", "file_modified": "File Modified", "version_less_than_0_45": "Cursor Version < 0.45.0, Skip Patching getMachineId", "detecting_version": "Detecting Cursor Version", "patching_getmachineid": "Patching getMachineId", "version_greater_than_0_45": "Cursor Version >= 0.45.0, Patching getMachineId", "permission_denied": "Permission Denied: {error}", "backup_created": "Backup Created", "update_success": "Update Success", "update_failed": "Update Failed: {error}", "windows_machine_guid_updated": "Windows Machine GUID Updated Successfully", "reading_package_json": "Reading package.json {path}", "invalid_json_object": "Invalid JSON Object", "no_version_field": "No Version Field Found in package.json", "version_field_empty": "Version Field is Empty", "invalid_version_format": "Invalid Version Format: {version}", "found_version": "Found Version: {version}", "version_parse_error": "Version Parse Error: {error}", "package_not_found": "Package.json Not Found: {path}", "check_version_failed": "Check Version Failed: {error}", "stack_trace": "Stack Trace", "version_too_low": "Cursor Version Too Low: {version} < 0.45.0", "no_write_permission": "No Write Permission: {path}", "path_not_found": "Path Not Found: {path}", "modify_file_failed": "Modify File Failed: {error}", "windows_machine_id_updated": "Windows Machine ID Updated Successfully", "update_windows_machine_id_failed": "Update Windows Machine ID Failed: {error}", "update_windows_machine_guid_failed": "Update Windows Machine GUID Failed: {error}", "file_not_found": "File Not Found: {path}"}, "register": {"title": "Cursor Registration Tool", "start": "Starting registration process...", "handling_turnstile": "Processing security verification...", "retry_verification": "Retrying verification...", "detect_turnstile": "Checking security verification...", "verification_success": "Security verification successful", "starting_browser": "Opening browser...", "form_success": "Form submitted successfully", "browser_started": "Browser opened successfully", "waiting_for_second_verification": "Waiting for email verification...", "waiting_for_verification_code": "Waiting for verification code...", "password_success": "Password set successfully", "password_error": "Could not set password: {error}. Please try again", "waiting_for_page_load": "Loading page...", "first_verification_passed": "Initial verification successful", "mailbox": "Successfully accessed email inbox", "register_start": "Start Register", "form_submitted": "Form Submitted, Start Verification...", "filling_form": "Fill Form", "visiting_url": "Visiting URL", "basic_info": "Basic Info Submitted", "handle_turnstile": "Handle Turnstile", "no_turnstile": "Not Detect Turnstile", "turnstile_passed": "<PERSON><PERSON><PERSON> Passed", "verification_start": "Start Getting Verification Code", "verification_timeout": "Get Verification Code Timeout", "verification_not_found": "No Verification Code Found", "try_get_code": "Try | {attempt} Get Verification Code | Time Remaining: {time}s", "get_account": "Getting Account Info", "get_token": "Get Cursor Session Token", "token_success": "Get Token Success", "token_attempt": "Try | {attempt} times to get Token | Will retry in {time}s", "token_max_attempts": "Reach Max Attempts ({max}) | Failed to get Token", "token_failed": "Get Token Failed: {error}", "account_error": "Get Account Info Failed: {error}", "press_enter": "Press Enter to Exit", "browser_start": "Starting Browser", "open_mailbox": "Opening Mailbox Page", "email_error": "Failed to Get Email Address", "setup_error": "Email Setup Error: {error}", "start_getting_verification_code": "Start Getting Verification Code, Will Try in 60s", "get_verification_code_timeout": "Get Verification Code Timeout", "get_verification_code_success": "Get Verification Code Success", "try_get_verification_code": "Try | {attempt} Get Verification Code | Time Remaining: {remaining_time}s", "verification_code_filled": "Verification Code Filled", "login_success_and_jump_to_settings_page": "Login Success and Jump to Settings Page", "detect_login_page": "Detect Login Page, Start Login...", "cursor_registration_completed": "Cursor Registration Completed!", "set_password": "Set Password", "basic_info_submitted": "Basic Info Submitted", "cursor_auth_info_updated": "<PERSON><PERSON><PERSON>th Info Updated", "cursor_auth_info_update_failed": "<PERSON><PERSON>or Auth Info Update Failed", "reset_machine_id": "Reset Machine ID", "account_info_saved": "Account Info Saved", "save_account_info_failed": "Save Account Info Failed", "get_email_address": "Get Email Address", "update_cursor_auth_info": "Update <PERSON><PERSON><PERSON> Info", "register_process_error": "Register Process Error: {error}", "setting_password": "Setting Password", "manual_code_input": "Manual Code Input", "manual_email_input": "Manual Email Input", "suggest_email": "Suggested email: {suggested_email}", "use_suggested_email_or_enter": "Type \"yes\" to use this email or enter your own email:", "password": "Password", "first_name": "First Name", "last_name": "Last Name", "exit_signal": "Exit Signal", "email_address": "Email Address", "config_created": "Config Created", "verification_failed": "Verification Failed", "verification_error": "Verification Error: {error}", "config_option_added": "Config Option Added: {option}", "config_updated": "Config Updated", "password_submitted": "Password Submitted", "total_usage": "Total Usage: {usage}", "setting_on_password": "Setting Password", "getting_code": "Getting Verification Code, <PERSON> in 60s", "human_verify_error": "Can't verify the user is human. Retrying...", "max_retries_reached": "Maximum retry attempts reached. Registration failed.", "browser_path_invalid": "{browser} path is invalid, using default path", "using_browser": "Using {browser} browser: {path}", "using_browser_profile": "Using {browser} profile from: {user_data_dir}", "make_sure_browser_is_properly_installed": "Make sure {browser} is properly installed", "try_install_browser": "Try installing the browser with your package manager", "tracking_processes": "Tracking {count} {browser} processes", "no_new_processes_detected": "No new {browser} processes detected to track", "could_not_track_processes": "Could not track {browser} processes: {error}", "using_tempmail_plus": "Using TempMailPlus for email verification", "tempmail_plus_enabled": "TempMailPlus is enabled", "tempmail_plus_disabled": "TempMailPlus is disabled", "tempmail_plus_config_missing": "TempMailPlus configuration is missing", "tempmail_plus_email_missing": "TempMailPlus email is not configured", "tempmail_plus_epin_missing": "TempMailPlus epin is not configured", "tempmail_plus_initialized": "TempMailPlus initialized successfully", "tempmail_plus_init_failed": "Failed to initialize TempMailPlus: {error}", "tempmail_plus_verification_started": "Starting TempMailPlus verification process", "tempmail_plus_verification_completed": "TempMailPlus verification completed successfully", "tempmail_plus_verification_failed": "TempMailPlus verification failed: {error}"}, "auth": {"title": "<PERSON><PERSON><PERSON>", "checking_auth": "Checking Auth File", "auth_not_found": "Auth File Not Found", "auth_file_error": "Auth File Error: {error}", "reading_auth": "Reading Auth File", "updating_auth": "Updating Auth Info", "auth_updated": "Auth Info Updated Successfully", "auth_update_failed": "Auth Info Update Failed: {error}", "auth_file_created": "Auth File Created", "auth_file_create_failed": "Auth File Create Failed: {error}", "press_enter": "Press Enter to Exit", "reset_machine_id": "Reset Machine ID", "database_connection_closed": "Database Connection Closed", "database_updated_successfully": "Database Updated Successfully", "connected_to_database": "Connected to Database", "updating_pair": "Updating Key-Value Pair", "db_not_found": "Database file not found at: {path}", "db_permission_error": "Cannot access database file. Please check permissions", "db_connection_error": "Failed to connect to database: {error}"}, "control": {"generate_email": "Generating New Email", "blocked_domain": "Blocked Domain", "select_domain": "Selecting Random Domain", "copy_email": "<PERSON><PERSON><PERSON> Email Address", "enter_mailbox": "Entering Mailbox", "refresh_mailbox": "Refreshing Mailbox", "check_verification": "Checking Verification Code", "verification_found": "Verification Code Found", "verification_not_found": "No Verification Code Found", "browser_error": "Browser Control Error: {error}", "navigation_error": "Navigation Error: {error}", "email_copy_error": "Email Copy Error: {error}", "mailbox_error": "Mailbox Error: {error}", "token_saved_to_file": "Token Saved to cursor_tokens.txt", "navigate_to": "Navigating to {url}", "generate_email_success": "Generate Email Success", "select_email_domain": "Select Email Domain", "select_email_domain_success": "Select Email Domain Success", "get_email_name": "Get Email Name", "get_email_name_success": "Get Email Name Success", "get_email_address": "Get Email Address", "get_email_address_success": "Get Email Address Success", "enter_mailbox_success": "Enter Mailbox Success", "found_verification_code": "Found Verification Code", "get_cursor_session_token": "Get Cursor Session Token", "get_cursor_session_token_success": "Get Cursor Session Token Success", "get_cursor_session_token_failed": "Get Cursor Session Token Failed", "save_token_failed": "Save Token Failed", "database_updated_successfully": "Database Updated Successfully", "database_connection_closed": "Database Connection Closed", "no_valid_verification_code": "No Valid Verification Code"}, "email": {"starting_browser": "Starting Browser", "visiting_site": "Visiting mail domains", "create_success": "Email Created Successfully", "create_failed": "Failed to C<PERSON> Em<PERSON>", "create_error": "Email Creation Error: {error}", "refreshing": "Refreshing Email", "refresh_success": "Email Refreshed Successfully", "refresh_error": "Email Refresh Error: {error}", "refresh_button_not_found": "Refresh <PERSON><PERSON> Not Found", "verification_found": "Verification Found", "verification_not_found": "Verification Not Found", "verification_error": "Verification Error: {error}", "verification_code_found": "Verification Code Found", "verification_code_not_found": "Verification Code Not Found", "verification_code_error": "Verification Code Error: {error}", "address": "Email Address", "all_domains_blocked": "All Domains Blocked, Switching Service", "no_available_domains_after_filtering": "No Available Domains After Filtering", "switching_service": "Switching to {service} Service", "domains_list_error": "Failed to Get Domains List: {error}", "failed_to_get_available_domains": "Failed to Get Available Domains", "domains_excluded": "Domains Excluded: {domains}", "failed_to_create_account": "Failed to Create Account", "account_creation_error": "Account Creation Error: {error}", "blocked_domains": "Blocked Domains: {domains}", "blocked_domains_loaded": "Blocked Domains Loaded: {count}", "blocked_domains_loaded_error": "Blocked Domains Loaded Error: {error}", "blocked_domains_loaded_success": "Blocked Domains Loaded Successfully", "blocked_domains_loaded_timeout": "Blocked Domains Loaded Timeout: {timeout}s", "blocked_domains_loaded_timeout_error": "Blocked Domains Loaded Timeout Error: {error}", "available_domains_loaded": "Available Domains Loaded: {count}", "domains_filtered": "Domains Filtered: {count}", "trying_to_create_email": "Trying to create email: {email}", "domain_blocked": "Domain Blocked: {domain}", "using_chrome_profile": "Using Chrome profile from: {user_data_dir}", "no_display_found": "No display found. Make sure X server is running.", "try_export_display": "Try: export DISPLAY=:0", "extension_load_error": "Extension Load Error: {error}", "make_sure_chrome_chromium_is_properly_installed": "Make sure Chrome/Chromium is properly installed", "try_install_chromium": "Try: sudo apt install chromium-browser"}, "update": {"title": "Disable Cursor Auto Update", "disable_success": "Auto Update Disabled Successfully", "disable_failed": "Disable Auto Update Failed: {error}", "press_enter": "Press Enter to Exit", "start_disable": "Start Disabling Auto Update", "killing_processes": "Killing Processes", "processes_killed": "Processes Killed", "removing_directory": "Removing Directory", "directory_removed": "Directory Removed", "creating_block_file": "Creating Block File", "block_file_created": "Block File Created", "clearing_update_yml": "Clearing update.yml file", "update_yml_cleared": "update.yml file cleared", "update_yml_not_found": "update.yml file not found", "clear_update_yml_failed": "Failed to clear update.yml file: {error}", "unsupported_os": "Unsupported OS: {system}", "remove_directory_failed": "Failed to remove directory: {error}", "create_block_file_failed": "Failed to create block file: {error}", "directory_locked": "Directory is locked: {path}", "yml_locked": "update.yml file is locked", "block_file_locked": "block file is locked", "yml_already_locked": "update.yml file is already locked", "block_file_already_locked": "block file is already locked", "block_file_locked_error": "Block file locked error: {error}", "yml_locked_error": "update.yml file locked error: {error}", "block_file_already_locked_error": "Block file already locked error: {error}", "yml_already_locked_error": "update.yml file already locked error: {error}"}, "updater": {"checking": "Checking for updates...", "new_version_available": "New version available! (Current: {current}, Latest: {latest})", "updating": "Updating to the latest version. The program will restart automatically.", "up_to_date": "You are using the latest version.", "check_failed": "Failed to check for updates: {error}", "continue_anyway": "Continuing with current version...", "update_confirm": "Do you want to update to the latest version? (Y/n)", "update_skipped": "Skipping update.", "invalid_choice": "Invalid choice. Please enter 'Y' or 'n'.", "development_version": "Development Version {current} > {latest}", "changelog_title": "Changelog", "rate_limit_exceeded": "GitHub API rate limit exceeded. Skipping update check."}, "totally_reset": {"title": "Totally Reset <PERSON>ursor", "checking_config": "Checking Config File", "config_not_found": "Config File Not Found", "no_permission": "Cannot Read or Write Config File, Please Check File Permissions", "reading_config": "Reading Current Config", "creating_backup": "Creating Config <PERSON>up", "backup_exists": "Backup File Already Exists, Skipping Backup Step", "generating_new_machine_id": "Generating New Machine ID", "saving_new_config": "Saving New Config to JSON", "success": "<PERSON><PERSON><PERSON>set Successfully", "error": "Cursor Reset Failed: {error}", "press_enter": "Press Enter to Exit", "reset_machine_id": "Reset Machine ID", "database_connection_closed": "Database Connection Closed", "database_updated_successfully": "Database Updated Successfully", "connected_to_database": "Connected to Database", "updating_pair": "Updating Key-Value Pair", "db_not_found": "Database file not found at: {path}", "db_permission_error": "Cannot access database file. Please check permissions", "db_connection_error": "Failed to connect to database: {error}", "feature_title": "FEATURES", "feature_1": "Complete removal of Cursor AI settings and configurations", "feature_2": "Clears all cached data including AI history and prompts", "feature_3": "Resets machine ID to bypass trial detection", "feature_4": "Creates new randomized machine identifiers", "feature_5": "Removes custom extensions and preferences", "feature_6": "Resets trial information and activation data", "feature_7": "Deep scan for hidden license and trial-related files", "feature_8": "Safely preserves non-Cursor files and applications", "feature_9": "Compatible with Windows, macOS, and Linux", "disclaimer_title": "DISCLAIMER", "disclaimer_1": "This tool will permanently delete all Cursor AI settings,", "disclaimer_2": "configurations, and cached data. This action cannot be undone.", "disclaimer_3": "Your code files will NOT be affected, and the tool is designed", "disclaimer_4": "to only target Cursor AI editor files and trial detection mechanisms.", "disclaimer_5": "Other applications on your system will not be affected.", "disclaimer_6": "You will need to set up Cursor AI again after running this tool.", "disclaimer_7": "Use at your own risk", "confirm_title": "Are you sure you want to proceed?", "confirm_1": "This action will delete all Cursor AI settings,", "confirm_2": "configurations, and cached data. This action cannot be undone.", "confirm_3": "Your code files will NOT be affected, and the tool is designed", "confirm_4": "to only target Cursor AI editor files and trial detection mechanisms.", "confirm_5": "Other applications on your system will not be affected.", "confirm_6": "You will need to set up Cursor AI again after running this tool.", "confirm_7": "Use at your own risk", "invalid_choice": "Please enter 'Y' or 'n'", "skipped_for_safety": "Skipped for safety (not Cursor-related): {path}", "deleted": "Deleted: {path}", "error_deleting": "Error deleting {path}: {error}", "not_found": "File not found: {path}", "resetting_machine_id": "Resetting machine identifiers to bypass trial detection...", "created_machine_id": "Created new machine ID: {path}", "error_creating_machine_id": "Error creating machine ID file {path}: {error}", "error_searching": "Error searching for files in {path}: {error}", "created_extended_trial_info": "Created new extended trial info: {path}", "error_creating_trial_info": "Error creating trial info file {path}: {error}", "resetting_cursor_ai_editor": "Resetting Cursor AI Editor... Please wait.", "reset_cancelled": "Reset cancelled. Exiting without making any changes.", "windows_machine_id_modification_skipped": "Windows machine ID modification skipped: {error}", "linux_machine_id_modification_skipped": "Linux machine-id modification skipped: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Note: Complete machine ID reset may require running as administrator", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Note: Complete system machine-id reset may require sudo privileges", "windows_registry_instructions": "📝 NOTE: For complete reset on Windows, you might also need to clean registry entries.", "windows_registry_instructions_2": "   Run 'regedit' and search for keys containing '<PERSON>ursor' or 'Cursor<PERSON>I' under HKEY_CURRENT_USER\\Software\\ and delete them.\n", "reset_log_1": "Cursor AI has been fully reset and trial detection bypassed!", "reset_log_2": "Please restart your system for changes to take effect.", "reset_log_3": "You will need to reinstall Cursor AI and should now have a fresh trial period.", "reset_log_4": "For best results, consider also:", "reset_log_5": "Use a different email address when registering for a new trial", "reset_log_6": "If available, use a VPN to change your IP address", "reset_log_7": "Clear your browser cookies and cache before visiting Cursor AI's website", "reset_log_8": "If issues persist, try installing Cursor AI in a different location", "reset_log_9": "If you encounter any issues, go to Github Issue Tracker and create an issue at https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "An unexpected error occurred: {error}", "report_issue": "Please report this issue to Github Issue Tracker at https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "Process interrupted by user. Exiting...", "return_to_main_menu": "Returning to main menu...", "process_interrupted": "Process interrupted. Exiting...", "press_enter_to_return_to_main_menu": "Press Enter to return to main menu...", "removing_known": "Removing known trial/license files", "performing_deep_scan": "Performing deep scan for additional trial/license files", "found_additional_potential_license_trial_files": "Found {count} additional potential license/trial files", "checking_for_electron_localstorage_files": "Checking for Electron localStorage files", "no_additional_license_trial_files_found_in_deep_scan": "No additional license/trial files found in deep scan", "removing_electron_localstorage_files": "Removing Electron localStorage files", "electron_localstorage_files_removed": "Electron localStorage files removed", "electron_localstorage_files_removal_error": "Error removing Electron localStorage files: {error}", "removing_electron_localstorage_files_completed": "Electron localStorage files removal completed", "warning_title": "WARNING", "warning_1": "This action will delete all Cursor AI settings,", "warning_2": "configurations, and cached data. This action cannot be undone.", "warning_3": "Your code files will NOT be affected, and the tool is designed", "warning_4": "to only target Cursor AI editor files and trial detection mechanisms.", "warning_5": "Other applications on your system will not be affected.", "warning_6": "You will need to set up Cursor AI again after running this tool.", "warning_7": "Use at your own risk", "removed": "Removed: {path}", "failed_to_reset_machine_guid": "Failed to reset machine GUID", "failed_to_remove": "Failed to remove: {path}", "failed_to_delete_file": "Failed to delete file: {path}", "failed_to_delete_directory": "Failed to delete directory: {path}", "failed_to_delete_file_or_directory": "Failed to delete file or directory: {path}", "deep_scanning": "Performing deep scan for additional trial/license files", "resetting_cursor": "Resetting Cursor AI Editor... Please wait.", "completed_in": "Completed in {time} seconds", "cursor_reset_completed": "Cursor AI Editor has been fully reset and trial detection bypassed!", "cursor_reset_failed": "Cursor AI Editor reset failed: {error}", "cursor_reset_cancelled": "Cursor AI Editor reset cancelled. Exiting without making any changes.", "operation_cancelled": "Operation cancelled. Exiting without making any changes.", "navigating_to_settings": "Navigating to settings page...", "already_on_settings": "Already on settings page", "login_redirect_failed": "Login redirection failed, trying direct navigation...", "advanced_tab_not_found": "Advanced tab not found after multiple attempts", "advanced_tab_retry": "Advanced tab not found, attempt {attempt}/{max_attempts}", "advanced_tab_error": "Error finding Advanced tab: {error}", "advanced_tab_clicked": "Clicked on Advanced tab", "direct_advanced_navigation": "Trying direct navigation to advanced tab", "delete_button_not_found": "Delete Account button not found after multiple attempts", "delete_button_retry": "Delete button not found, attempt {attempt}/{max_attempts}", "delete_button_error": "Error finding Delete button: {error}", "delete_button_clicked": "Clicked on Delete Account button", "found_danger_zone": "Found Danger Zone section", "delete_input_not_found": "Delete confirmation input not found after multiple attempts", "delete_input_retry": "Delete input not found, attempt {attempt}/{max_attempts}", "delete_input_error": "Error finding Delete input: {error}", "delete_input_not_found_continuing": "Delete confirmation input not found, trying to continue anyway"}, "github_register": {"title": "GitHub + Cursor AI Registration Automation", "features_header": "Features", "feature1": "Generates a temporary email using 1secmail.", "feature2": "Registers a new GitHub account with random credentials.", "feature3": "Verifies the GitHub email automatically.", "feature4": "Logs into Cursor AI using GitHub authentication.", "feature5": "Resets the machine ID to bypass trial detection.", "feature6": "Saves all credentials to a file.", "warnings_header": "Warnings", "warning1": "This script automates account creation, which may violate GitHub/Cursor terms of service.", "warning2": "Requires internet access and administrative privileges.", "warning3": "CAPTCHA or additional verification may interrupt automation.", "warning4": "Use responsibly and at your own risk.", "confirm": "Are you sure you want to proceed?", "invalid_choice": "Invalid choice. Please enter 'yes' or 'no'", "cancelled": "Operation cancelled", "program_terminated": "Program terminated by user", "starting_automation": "Starting automation...", "github_username": "GitHub Username", "github_password": "GitHub Password", "email_address": "Email Address", "credentials_saved": "These credentials have been saved to github_cursor_accounts.txt", "completed_successfully": "GitHub + Cursor registration completed successfully!", "registration_encountered_issues": "GitHub + Cursor registration encountered issues.", "check_browser_windows_for_manual_intervention_or_try_again_later": "Check browser windows for manual intervention or try again later."}, "account_info": {"subscription": "Subscription", "trial_remaining": "Remaining Pro Trial", "days": "days", "subscription_not_found": "Subscription information not found", "email_not_found": "<PERSON><PERSON> not found", "failed_to_get_account": "Failed to get account information", "config_not_found": "Configuration not found.", "failed_to_get_usage": "Failed to get usage information", "failed_to_get_subscription": "Failed to get subscription information", "failed_to_get_email": "Failed to get email address", "failed_to_get_token": "Failed to get token", "failed_to_get_account_info": "Failed to get account information", "title": "Account Information", "email": "Email", "token": "Token", "usage": "Usage", "subscription_type": "Subscription Type", "remaining_trial": "Remaining Trial", "days_remaining": "Days Remaining", "premium": "Premium", "pro": "Pro", "pro_trial": "Pro Trial", "team": "Team", "enterprise": "Enterprise", "free": "Free", "active": "Active", "inactive": "Inactive", "premium_usage": "Premium Usage", "basic_usage": "Basic Usage", "usage_not_found": "Usage not found", "lifetime_access_enabled": "Lifetime Access Enabled", "token_not_found": "Token not found"}, "config": {"config_not_available": "Configuration not available", "configuration": "Configuration", "enabled": "Enabled", "disabled": "Disabled", "config_directory": "Config Directory", "neither_cursor_nor_cursor_directory_found": "Neither Cursor nor Cursor directory found in {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Please make sure <PERSON><PERSON><PERSON> is installed and has been run at least once", "storage_directory_not_found": "Storage directory not found: {storage_dir}", "storage_file_found": "Storage file found: {storage_path}", "file_size": "File size: {size} bytes", "file_permissions": "File permissions: {permissions}", "file_owner": "File owner: {owner}", "file_group": "File group: {group}", "error_getting_file_stats": "Error getting file stats: {error}", "permission_denied": "Permission denied: {storage_path}", "try_running": "Try running: {command}", "and": "And", "storage_file_is_empty": "Storage file is empty: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "The file might be corrupted, please reinstall Cursor", "storage_file_not_found": "Storage file not found: {storage_path}", "error_checking_linux_paths": "Error checking Linux paths: {error}", "config_option_added": "Config option added: {option}", "config_updated": "Config updated", "config_created": "Config created: {config_file}", "config_setup_error": "Error setting up config: {error}", "storage_file_is_valid_and_contains_data": "Storage file is valid and contains data", "error_reading_storage_file": "Error reading storage file: {error}", "also_checked": "Also checked {path}", "backup_created": "Backup created: {path}", "config_removed": "Config file removed for forced update", "backup_failed": "Failed to backup config: {error}", "force_update_failed": "Force update config failed: {error}", "config_force_update_disabled": "Config file force update disabled , skipping forced update", "config_force_update_enabled": "Config file force update enabled , performing forced update", "documents_path_not_found": "Documents path not found, using current directory", "config_dir_created": "Config directory created: {path}", "using_temp_dir": "Using temporary directory due to error: {path} (Error: {error})"}, "oauth": {"authentication_button_not_found": "Authentication button not found", "authentication_failed": "Authentication failed: {error}", "found_cookies": "Found {count} cookies", "token_extraction_error": "Token extraction error: {error}", "authentication_successful": "Authentication successful - Email: {email}", "missing_authentication_data": "Missing authentication data: {data}", "failed_to_delete_account": "Failed to delete account: {error}", "invalid_authentication_type": "Invalid authentication type", "auth_update_success": "Auth update success", "browser_closed": "Browser closed", "auth_update_failed": "Auth update failed", "google_start": "Google start", "github_start": "<PERSON><PERSON><PERSON> start", "usage_count": "Usage count: {usage}", "account_has_reached_maximum_usage": "Account has reached maximum usage, {deleting}", "starting_new_authentication_process": "Starting new authentication process...", "failed_to_delete_expired_account": "Failed to delete expired account", "could_not_check_usage_count": "Could not check usage count: {error}", "found_email": "Found email: {email}", "could_not_find_email": "Could not find email: {error}", "could_not_find_usage_count": "Could not find usage count: {error}", "already_on_settings_page": "Already on settings page!", "failed_to_extract_auth_info": "Failed to extract auth info: {error}", "no_chrome_profiles_found": "No Chrome profiles found, using Default", "found_default_chrome_profile": "Found Default Chrome profile", "using_first_available_chrome_profile": "Using first available Chrome profile: {profile}", "error_finding_chrome_profile": "Error finding Chrome profile, using Default: {error}", "initializing_browser_setup": "Initializing browser setup...", "detected_platform": "Detected platform: {platform}", "running_as_root_warning": "Running as root is not recommended for browser automation", "consider_running_without_sudo": "Consider running the script without sudo", "no_compatible_browser_found": "No compatible browser found. Please install Google Chrome or Chromium.", "supported_browsers": "Supported browsers for {platform}", "using_browser_profile": "Using browser profile: {profile}", "starting_browser": "Starting browser at: {path}", "browser_setup_completed": "Browser setup completed successfully", "browser_setup_failed": "Browser setup failed: {error}", "try_running_without_sudo_admin": "Try running without sudo/administrator privileges", "redirecting_to_authenticator_cursor_sh": "Redirecting to authenticator.cursor.sh...", "starting_google_authentication": "Starting Google authentication...", "starting_github_authentication": "Starting GitHub authentication...", "waiting_for_authentication": "Waiting for authentication...", "page_changed_checking_auth": "Page changed, checking auth...", "status_check_error": "Status check error: {error}", "authentication_timeout": "Authentication timeout", "account_is_still_valid": "Account is still valid (Usage: {usage})", "starting_re_authentication_process": "Starting re-authentication process...", "starting_new_google_authentication": "Starting new Google authentication...", "failed_to_delete_account_or_re_authenticate": "Failed to delete account or re-authenticate: {error}", "navigating_to_authentication_page": "Navigating to authentication page...", "please_select_your_google_account_to_continue": "Please select your Google account to continue...", "found_browser_data_directory": "Found browser data directory: {path}", "authentication_successful_getting_account_info": "Authentication successful, getting account info...", "warning_could_not_kill_existing_browser_processes": "Warning: Could not kill existing browser processes: {error}", "browser_failed_to_start": "<PERSON><PERSON><PERSON> failed to start: {error}", "browser_failed": "<PERSON><PERSON><PERSON> failed to start: {error}", "browser_failed_to_start_fallback": "<PERSON><PERSON><PERSON> failed to start: {error}", "user_data_dir_not_found": "{browser} user data directory not found at {path}, will try Chrome instead", "error_getting_user_data_directory": "Error getting user data directory: {error}", "warning_browser_close": "Warning: This will close all running {browser} processes", "killing_browser_processes": "Killing {browser} processes...", "profile_selection_error": "Error during profile selection: {error}", "using_configured_browser_path": "Using configured {browser} path: {path}", "browser_not_found_trying_chrome": "Could not find {browser}, trying Chrome instead", "found_chrome_at": "Found Chrome at: {path}", "found_browser_user_data_dir": "Found {browser} user data directory: {path}"}, "browser_profile": {"title": "Browser Profile Selection", "select_profile": "Select {browser} profile to use:", "profile_list": "Available {browser} profiles:", "default_profile": "Default profile", "profile": "Profile {number}", "no_profiles": "No {browser} profiles found", "error_loading": "Error loading {browser} profiles: {error}", "profile_selected": "Selected profile: {profile}", "invalid_selection": "Invalid selection. Please try again."}, "account_delete": {"title": "Cursor Google Account Deletion Tool", "warning": "WARNING: This will permanently delete your Cursor account. This action cannot be undone.", "cancelled": "Account deletion cancelled.", "starting_process": "Starting account deletion process...", "google_button_not_found": "Google login button not found", "logging_in": "Logging in with Google...", "waiting_for_auth": "Waiting for Google authentication...", "login_successful": "Login successful", "unexpected_page": "Unexpected page after login: {url}", "trying_settings": "Trying to navigate to settings page...", "select_google_account": "Please select your Google account...", "auth_timeout": "Authentication timeout, continuing anyway...", "navigating_to_settings": "Navigating to settings page...", "already_on_settings": "Already on settings page", "login_redirect_failed": "Login redirection failed, trying direct navigation...", "advanced_tab_not_found": "Advanced tab not found after multiple attempts", "advanced_tab_retry": "Advanced tab not found, attempt {attempt}/{max_attempts}", "advanced_tab_error": "Error finding Advanced tab: {error}", "advanced_tab_clicked": "Clicked on Advanced tab", "direct_advanced_navigation": "Trying direct navigation to advanced tab", "delete_button_not_found": "Delete Account button not found after multiple attempts", "delete_button_retry": "Delete button not found, attempt {attempt}/{max_attempts}", "delete_button_error": "Error finding Delete button: {error}", "delete_button_clicked": "Clicked on Delete Account button", "found_danger_zone": "Found Danger Zone section", "delete_input_not_found": "Delete confirmation input not found after multiple attempts", "delete_input_retry": "Delete input not found, attempt {attempt}/{max_attempts}", "delete_input_error": "Error finding Delete input: {error}", "delete_input_not_found_continuing": "Delete confirmation input not found, trying to continue anyway", "typed_delete": "Typed \"Delete\" in confirmation box", "confirm_button_not_found": "Confirm button not found after multiple attempts", "confirm_button_retry": "Confirm button not found, attempt {attempt}/{max_attempts}", "confirm_button_error": "Error finding Confirm button: {error}", "account_deleted": "Account deleted successfully!", "error": "Error during account deletion: {error}", "success": "Your Cursor account has been successfully deleted!", "failed": "Account deletion process failed or was cancelled.", "interrupted": "Account deletion process interrupted by user.", "unexpected_error": "Unexpected error: {error}", "found_email": "Found email: {email}", "email_not_found": "Email not found: {error}", "confirm_prompt": "Are you sure you want to proceed? (y/N): "}, "bypass": {"starting": "Starting Cursor version bypass...", "found_product_json": "Found product.json: {path}", "no_write_permission": "No write permission for file: {path}", "read_failed": "Failed to read product.json: {error}", "current_version": "Current version: {version}", "backup_created": "Backup created: {path}", "version_updated": "Version updated from {old} to {new}", "write_failed": "Failed to write product.json: {error}", "no_update_needed": "No update needed. Current version {version} is already >= 0.46.0", "bypass_failed": "Version bypass failed: {error}", "stack_trace": "Stack trace", "localappdata_not_found": "LOCALAPPDATA environment variable not found", "product_json_not_found": "product.json not found in common Linux paths", "unsupported_os": "Unsupported operating system: {system}", "file_not_found": "File not found: {path}", "title": "Cursor Version Bypass Tool", "description": "This tool modifies Cursor's product.json to bypass version restrictions", "menu_option": "Bypass Cursor Version Check"}, "auth_check": {"checking_authorization": "Checking authorization...", "token_source": "Get token from database or input manually? (d/m, default: d)", "getting_token_from_db": "Getting token from database...", "token_found_in_db": "Token found in database", "token_not_found_in_db": "Token not found in database", "cursor_acc_info_not_found": "cursor_acc_info.py not found", "error_getting_token_from_db": "Error getting token from database: {error}", "enter_token": "Enter your Cursor token: ", "token_length": "Token length: {length} characters", "usage_response_status": "Usage response status: {response}", "unexpected_status_code": "Unexpected status code: {code}", "jwt_token_warning": "Token appears to be in JWT format, but API check returned an unexpected status code. The token might be valid but API access is restricted.", "invalid_token": "Invalid token", "user_authorized": "User is authorized", "user_unauthorized": "User is unauthorized", "request_timeout": "Request timed out", "connection_error": "Connection error", "check_error": "Error checking authorization: {error}", "authorization_successful": "Authorization successful!", "authorization_failed": "Authorization failed!", "operation_cancelled": "Operation cancelled by user", "unexpected_error": "Unexpected error: {error}", "error_generating_checksum": "Error generating checksum: {error}", "checking_usage_information": "Checking usage information...", "check_usage_response": "Check usage response: {response}", "usage_response": "Usage response: {response}"}, "bypass_token_limit": {"title": "Bypass Token Limit Tool", "description": "This tool modifies the workbench.desktop.main.js file to bypass the token limit", "press_enter": "Press Enter to continue..."}, "token": {"refreshing": "Refreshing token...", "refresh_success": "Token refreshed successfully! Valid for {days} days (expires: {expire})", "no_access_token": "No access token in response", "refresh_failed": "Token refresh failed: {error}", "invalid_response": "Invalid JSON response from refresh server", "server_error": "Refresh server error: HTTP {status}", "request_timeout": "Request to refresh server timed out", "connection_error": "Connection error to refresh server", "unexpected_error": "Unexpected error during token refresh: {error}", "extraction_error": "Error extracting token: {error}"}, "restore": {"title": "Restore Machine ID from Backup", "starting": "Starting Machine ID Restore Process", "no_backups_found": "No backup files found", "available_backups": "Available backup files", "select_backup": "Select backup to restore", "to_cancel": "to cancel", "operation_cancelled": "Operation cancelled", "invalid_selection": "Invalid selection", "please_enter_number": "Please enter a valid number", "missing_id": "Missing ID: {id}", "read_backup_failed": "Failed to read backup file: {error}", "current_file_not_found": "Current storage file not found", "current_backup_created": "Created backup of current storage file", "storage_updated": "Storage file updated successfully", "update_failed": "Failed to update storage file: {error}", "sqlite_not_found": "SQLite database not found", "updating_sqlite": "Updating SQLite database", "updating_pair": "Updating key-value pair", "sqlite_updated": "SQLite database updated successfully", "sqlite_update_failed": "Failed to update SQLite database: {error}", "machine_id_backup_created": "Created backup of machineId file", "backup_creation_failed": "Failed to create backup: {error}", "machine_id_updated": "machineId file updated successfully", "machine_id_update_failed": "Failed to update machineId file: {error}", "updating_system_ids": "Updating system IDs", "system_ids_update_failed": "Failed to update system IDs: {error}", "permission_denied": "Permission denied. Please try running as administrator", "windows_machine_guid_updated": "Windows Machine GUID updated successfully", "update_windows_machine_guid_failed": "Failed to update Windows Machine GUID: {error}", "windows_machine_id_updated": "Windows Machine ID updated successfully", "update_windows_machine_id_failed": "Failed to update Windows Machine ID: {error}", "sqm_client_key_not_found": "SQMClient registry key not found", "update_windows_system_ids_failed": "Failed to update Windows system IDs: {error}", "macos_platform_uuid_updated": "macOS Platform UUID updated successfully", "failed_to_execute_plutil_command": "Failed to execute plutil command", "update_macos_system_ids_failed": "Failed to update macOS system IDs: {error}", "ids_to_restore": "Machine IDs to restore", "confirm": "Are you sure you want to restore these IDs?", "success": "Machine ID restored successfully", "process_error": "Restore process error: {error}", "press_enter": "Press Enter to continue"}, "manual_auth": {"title": "Manual Cursor Authentication", "token_prompt": "Enter your Cursor token (access_token/refresh_token):", "token_required": "Token is required", "verifying_token": "Verifying token validity...", "token_verified": "Token verified successfully!", "invalid_token": "Invalid token. Authentication aborted.", "token_verification_skipped": "Token verification skipped (check_user_authorized.py not found)", "token_verification_error": "Error verifying token: {error}", "continue_anyway": "Continue anyway? (y/N): ", "email_prompt": "Enter email (leave blank for random email):", "random_email_generated": "Random email generated: {email}", "auth_type_prompt": "Select authentication type:", "auth_type_auth0": "Auth_0 (<PERSON><PERSON><PERSON>)", "auth_type_google": "Google", "auth_type_github": "GitHub", "auth_type_selected": "Selected authentication type: {type}", "confirm_prompt": "Please confirm the following information:", "proceed_prompt": "Proceed? (y/N): ", "operation_cancelled": "Operation cancelled", "updating_database": "Updating Cursor authentication database...", "auth_updated_successfully": "Authentication information updated successfully!", "auth_update_failed": "Failed to update authentication information", "error": "Error: {error}"}, "tempmail": {"check_email_failed": "Check email failed: {error}", "extract_code_failed": "Extract verification code failed: {error}", "configured_email": "Configured email: {email}", "checking_email": "Checking for Cursor verification email...", "email_found": "Found Cursor verification email", "verification_code": "Verification code: {code}", "no_code": "Could not get verification code", "no_email": "No Cursor verification email found", "config_error": "Config file error: {error}", "general_error": "An error occurred: {error}"}}