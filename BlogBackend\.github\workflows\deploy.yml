name: Deploy BlogBackend

on:
  push:
    branches:
      - main  # 你实际用的分支

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'  # 你的Node版本

      - name: Install dependencies
        run: npm install

      - name: Copy files to server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "."
          target: ${{ secrets.TARGET_DIR }}
          strip_components: 0

      - name: SSH and setup environment
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ secrets.TARGET_DIR }}
            
            # 创建.env文件
            cat > .env << 'EOF'
            DB_DIALECT=mysql
            DB_HOST=${{ secrets.SERVER_IP }}
            DB_PORT=3306
            DB_NAME=blogDb
            DB_USER=blog_user
            DB_PASSWORD=${{ secrets.DB_PASSWORD }}
            PORT=3000
            NODE_ENV=production
            JWT_SECRET=${{ secrets.JWT_SECRET }}
            JWT_EXPIRES_IN=1d
            CORS_ORIGIN=http://localhost:3001
            EOF
            
            # 设置文件权限
            chmod 600 .env
            
            # 安装依赖
            npm install --production
            
            # 重启服务
            pm2 reload blog || pm2 start bin/www --name blog
            
            # 检查环境变量
            echo "=== 检查环境变量 ==="
            node -e "require('dotenv').config(); console.log('JWT_SECRET:', process.env.JWT_SECRET ? '已设置' : '未设置'); console.log('DB_HOST:', process.env.DB_HOST); console.log('NODE_ENV:', process.env.NODE_ENV);"