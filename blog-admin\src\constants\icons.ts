// 常用后台管理系统图标列表
export const ICON_LIST = [
  { name: 'Dashboard', icon: 'DashboardOutlined', value: 'DashboardOutlined' },
  { name: '用户管理', icon: 'UserOutlined', value: 'UserOutlined' },
  { name: '角色管理', icon: 'TeamOutlined', value: 'TeamOutlined' },
  { name: '权限管理', icon: 'SafetyCertificateOutlined', value: 'SafetyCertificateOutlined' },
  { name: '菜单管理', icon: 'MenuOutlined', value: 'MenuOutlined' },
  { name: '系统设置', icon: 'SettingOutlined', value: 'SettingOutlined' },
  { name: '日志管理', icon: 'FileTextOutlined', value: 'FileTextOutlined' },
  { name: '数据统计', icon: 'BarChartOutlined', value: 'BarChartOutlined' },
  { name: '文件管理', icon: 'FolderOutlined', value: 'FolderOutlined' },
  { name: '消息通知', icon: 'BellOutlined', value: 'BellOutlined' },
  { name: '帮助文档', icon: 'QuestionCircleOutlined', value: 'QuestionCircleOutlined' },
  { name: '退出登录', icon: 'LogoutOutlined', value: 'LogoutOutlined' },
  { name: '首页', icon: 'HomeOutlined', value: 'HomeOutlined' },
  { name: '列表', icon: 'UnorderedListOutlined', value: 'UnorderedListOutlined' },
  { name: '表格', icon: 'TableOutlined', value: 'TableOutlined' },
  { name: '表单', icon: 'FormOutlined', value: 'FormOutlined' },
  { name: '详情', icon: 'FileOutlined', value: 'FileOutlined' },
  { name: '编辑', icon: 'EditOutlined', value: 'EditOutlined' },
  { name: '删除', icon: 'DeleteOutlined', value: 'DeleteOutlined' },
  { name: '添加', icon: 'PlusOutlined', value: 'PlusOutlined' },
  { name: '搜索', icon: 'SearchOutlined', value: 'SearchOutlined' },
  { name: '刷新', icon: 'ReloadOutlined', value: 'ReloadOutlined' },
  { name: '下载', icon: 'DownloadOutlined', value: 'DownloadOutlined' },
  { name: '上传', icon: 'UploadOutlined', value: 'UploadOutlined' },
  { name: '导出', icon: 'ExportOutlined', value: 'ExportOutlined' },
  { name: '导入', icon: 'ImportOutlined', value: 'ImportOutlined' },
  { name: '打印', icon: 'PrinterOutlined', value: 'PrinterOutlined' },
  { name: '预览', icon: 'EyeOutlined', value: 'EyeOutlined' },
  { name: '复制', icon: 'CopyOutlined', value: 'CopyOutlined' },
  { name: '粘贴', icon: 'SnippetsOutlined', value: 'SnippetsOutlined' },
  { name: '保存', icon: 'SaveOutlined', value: 'SaveOutlined' },
  { name: '取消', icon: 'CloseOutlined', value: 'CloseOutlined' },
  { name: '确认', icon: 'CheckOutlined', value: 'CheckOutlined' },
  { name: '警告', icon: 'WarningOutlined', value: 'WarningOutlined' },
  { name: '错误', icon: 'CloseCircleOutlined', value: 'CloseCircleOutlined' },
  { name: '成功', icon: 'CheckCircleOutlined', value: 'CheckCircleOutlined' },
  { name: '链接', icon: 'LinkOutlined', value: 'LinkOutlined' },
  { name: '日历', icon: 'CalendarOutlined', value: 'CalendarOutlined' },
  { name: '时钟', icon: 'ClockCircleOutlined', value: 'ClockCircleOutlined' },
  { name: '地图', icon: 'EnvironmentOutlined', value: 'EnvironmentOutlined' },
  { name: '标签', icon: 'TagsOutlined', value: 'TagsOutlined' },
  { name: '分类', icon: 'AppstoreOutlined', value: 'AppstoreOutlined' },
  { name: '工具', icon: 'ToolOutlined', value: 'ToolOutlined' },
  { name: '数据库', icon: 'DatabaseOutlined', value: 'DatabaseOutlined' },
  { name: '服务器', icon: 'CloudServerOutlined', value: 'CloudServerOutlined' },
  { name: '网络', icon: 'GlobalOutlined', value: 'GlobalOutlined' },
  { name: '安全', icon: 'SecurityScanOutlined', value: 'SecurityScanOutlined' },
  { name: '监控', icon: 'MonitorOutlined', value: 'MonitorOutlined' },
  { name: '备份', icon: 'CloudUploadOutlined', value: 'CloudUploadOutlined' },
  { name: '恢复', icon: 'CloudDownloadOutlined', value: 'CloudDownloadOutlined' },
  { name: '同步', icon: 'SyncOutlined', value: 'SyncOutlined' },
  { name: '配置', icon: 'ControlOutlined', value: 'ControlOutlined' },
  { name: '插件', icon: 'ApiOutlined', value: 'ApiOutlined' },
  { name: '主题', icon: 'SkinOutlined', value: 'SkinOutlined' },
  { name: '语言', icon: 'TranslationOutlined', value: 'TranslationOutlined' },
  { name: '反馈', icon: 'MessageOutlined', value: 'MessageOutlined' },
  { name: '关于', icon: 'InfoCircleOutlined', value: 'InfoCircleOutlined' },
];

// 根据搜索关键词过滤图标
export const filterIcons = (keyword: string) => {
  if (!keyword) return ICON_LIST;
  return ICON_LIST.filter(
    icon =>
      icon.name.toLowerCase().includes(keyword.toLowerCase()) ||
      icon.value.toLowerCase().includes(keyword.toLowerCase())
  );
};
