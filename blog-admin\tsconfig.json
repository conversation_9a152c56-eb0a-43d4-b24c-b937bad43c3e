{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/hooks/*": ["hooks/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/api/*": ["api/*"], "@/store/*": ["store/*"], "@/pages/*": ["pages/*"], "@/constants/*": ["constants/*"], "@/styles/*": ["styles/*"]}}, "include": ["src"]}