{"menu": {"title": "<PERSON><PERSON><PERSON>", "exit": "<PERSON><PERSON><PERSON><PERSON> Trình", "reset": "Đặt Lại ID Máy", "register": "<PERSON><PERSON><PERSON> K<PERSON> Cursor <PERSON>", "register_google": "<PERSON><PERSON><PERSON> Bằng <PERSON>", "register_github": "<PERSON><PERSON><PERSON> Bằng <PERSON>", "register_manual": "<PERSON><PERSON><PERSON> Ký Cursor <PERSON> Chỉnh", "quit": "Đóng Ứng Dụng Cursor", "select_language": "<PERSON><PERSON>", "select_chrome_profile": "<PERSON><PERSON>n <PERSON>ơ Chrome", "input_choice": "<PERSON><PERSON> lòng nhập lựa chọn của bạn ({choices})", "invalid_choice": "<PERSON><PERSON><PERSON> chọn không hợp lệ. <PERSON><PERSON> lòng nhập một số từ {choices}", "program_terminated": "<PERSON><PERSON><PERSON><PERSON> trình đã bị người dùng chấm dứt", "error_occurred": "<PERSON><PERSON> xảy ra lỗi: {error}. <PERSON><PERSON> lòng thử lại", "press_enter": "<PERSON>hấn Enter <PERSON>", "disable_auto_update": "Tắt tự động cập nh<PERSON><PERSON>", "lifetime_access_enabled": "ĐÃ BẬT TRUY CẬP TRỌN ĐỜI", "totally_reset": "Đặt lại hoàn toàn <PERSON>", "outdate": "<PERSON><PERSON><PERSON> c<PERSON>", "temp_github_register": "<PERSON><PERSON><PERSON> k<PERSON> tạm thời", "admin_required": "<PERSON><PERSON> chạy dư<PERSON><PERSON> dạng tệp thực thi, yê<PERSON> c<PERSON><PERSON> quyền quản trị.", "admin_required_continue": "<PERSON><PERSON><PERSON><PERSON> tục mà không có quyền quản trị.", "coming_soon": "S<PERSON><PERSON> ra mắt", "fixed_soon": "Sẽ <PERSON><PERSON><PERSON>", "contribute": "<PERSON><PERSON><PERSON>", "config": "<PERSON><PERSON><PERSON>", "delete_google_account": "Xóa Tài Khoản Google Cursor", "continue_prompt": "Ti<PERSON><PERSON> tục? (y/N): ", "operation_cancelled_by_user": "<PERSON><PERSON> t<PERSON>c đã bị người dùng hủy", "exiting": "<PERSON><PERSON> ……", "bypass_version_check": "Bỏ qua <PERSON><PERSON>m tra <PERSON> bả<PERSON>", "check_user_authorized": "<PERSON><PERSON><PERSON> tra <PERSON><PERSON> dùng", "bypass_token_limit": "Bỏ qua giới hạn <PERSON>ken", "language_config_saved": "<PERSON><PERSON><PERSON> ngôn ngữ thành công", "lang_invalid_choice": "<PERSON><PERSON><PERSON> chọn không hợp lệ. <PERSON><PERSON> lòng nhập một số từ ({lang_choices})", "restore_machine_id": "<PERSON><PERSON>ô<PERSON> phục ID máy từ bản sao lưu", "manual_custom_auth": "<PERSON><PERSON><PERSON> công tùy chỉnh auth"}, "languages": {"ar": "Tiếng Ả Rập", "en": "<PERSON><PERSON><PERSON><PERSON>", "zh_cn": "T<PERSON>ếng Trung Giản Thể", "zh_tw": "T<PERSON>ếng Trung Phồn Thể", "vi": "Tiếng <PERSON>", "tr": "<PERSON><PERSON><PERSON><PERSON>", "bg": "Tiếng Bulgaria", "nl": "<PERSON><PERSON>ếng <PERSON>", "de": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "pt": "Tiếng Bồ Đào Nha", "ru": "<PERSON><PERSON><PERSON><PERSON>", "es": "Tiếng Tâ<PERSON>", "it": "Ý", "ja": "<PERSON><PERSON><PERSON><PERSON>"}, "quit_cursor": {"start": "<PERSON><PERSON><PERSON>", "no_process": "<PERSON><PERSON><PERSON><PERSON> Trình Cursor <PERSON>", "terminating": "<PERSON><PERSON> Trình {pid}", "waiting": "<PERSON><PERSON>ờ Tiến <PERSON>", "success": "<PERSON><PERSON><PERSON>n T<PERSON>ursor <PERSON>", "timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {pids}", "error": "<PERSON><PERSON> Ra Lỗi: {error}"}, "reset": {"title": "Công Cụ Đặt Lại ID Máy Cursor", "checking": "<PERSON><PERSON><PERSON><PERSON>", "not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no_permission": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>ể <PERSON> Hoặc <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON> Tệp", "reading": "<PERSON><PERSON>", "creating_backup": "<PERSON><PERSON>", "backup_exists": "Tệ<PERSON>, Bỏ <PERSON><PERSON> Lư<PERSON>", "generating": "Đang Tạo ID Máy Mới", "saving_json": "<PERSON><PERSON> Vào JSON", "success": "Đặt Lại ID Máy Thành Công", "new_id": "ID Máy M<PERSON>i", "permission_error": "Lỗi Quyền: {error}", "run_as_admin": "<PERSON><PERSON>ử Chạy Chương Trình <PERSON> Quản Trị", "process_error": "Lỗi Quá Trình Đặt Lại: {error}", "updating_sqlite": "<PERSON><PERSON> Sở Dữ Liệu SQLite", "updating_pair": "<PERSON><PERSON> Cặp Khóa-Giá <PERSON>rị", "sqlite_success": "<PERSON><PERSON><PERSON> Sở Dữ Liệu SQLite Thành Công", "sqlite_error": "<PERSON><PERSON><PERSON> Sở <PERSON>ệu SQLite Thất <PERSON>: {error}", "press_enter": "<PERSON>hấn Enter <PERSON>", "unsupported_os": "<PERSON><PERSON> Được Hỗ Trợ: {os}", "linux_path_not_found": "<PERSON><PERSON><PERSON><PERSON> Dẫn Linux", "updating_system_ids": "<PERSON><PERSON>", "system_ids_updated": "<PERSON><PERSON><PERSON>ống T<PERSON>nh Công", "system_ids_update_failed": "<PERSON><PERSON><PERSON> <PERSON><PERSON>ống Thất <PERSON>: {error}", "windows_guid_updated": "<PERSON><PERSON>p <PERSON>t GUID Windows Thành Công", "windows_permission_denied": "Windows Từ <PERSON>", "windows_guid_update_failed": "<PERSON><PERSON>p <PERSON> GUID Windows Thất Bại", "macos_uuid_updated": "<PERSON><PERSON><PERSON> UUID macOS Thành Công", "plutil_command_failed": "<PERSON><PERSON><PERSON> plutil <PERSON>", "start_patching": "Bắt <PERSON> getMachineId", "macos_uuid_update_failed": "<PERSON><PERSON><PERSON> UUID macOS Thất Bại", "current_version": "<PERSON><PERSON><PERSON>: {version}", "patch_completed": "<PERSON><PERSON> getMachineId <PERSON>", "patch_failed": "<PERSON><PERSON> getMachineId Thất <PERSON>: {error}", "version_check_passed": "<PERSON><PERSON><PERSON>", "file_modified": "<PERSON><PERSON><PERSON>", "version_less_than_0_45": "<PERSON><PERSON><PERSON> < 0.45.0, Bỏ <PERSON>ua <PERSON> getMachineId", "detecting_version": "<PERSON><PERSON>", "patching_getmachineid": "Đang Vá getMachineId", "version_greater_than_0_45": "<PERSON><PERSON><PERSON> >= 0.45.0, <PERSON><PERSON> getMachineId", "permission_denied": "T<PERSON>ố<PERSON>: {error}", "backup_created": "Đã Tạo Bản Sao Lưu", "update_success": "<PERSON><PERSON><PERSON>", "update_failed": "<PERSON><PERSON><PERSON>: {error}", "windows_machine_guid_updated": "<PERSON><PERSON><PERSON>t GUID Máy Windows Thành Công", "reading_package_json": "<PERSON><PERSON> package.json {path}", "invalid_json_object": "<PERSON><PERSON><PERSON> JSON Không Hợp <PERSON>", "no_version_field": "Không <PERSON> Trường Phiên Bản Trong package.json", "version_field_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalid_version_format": "<PERSON><PERSON><PERSON>: {version}", "found_version": "<PERSON><PERSON> <PERSON><PERSON>: {version}", "version_parse_error": "Lỗi <PERSON><PERSON><PERSON>: {error}", "package_not_found": "<PERSON><PERSON><PERSON><PERSON> Package.json: {path}", "check_version_failed": "<PERSON><PERSON><PERSON>: {error}", "stack_trace": "<PERSON><PERSON><PERSON>", "version_too_low": "<PERSON><PERSON><PERSON>: {version} < 0.45.0", "no_write_permission": "<PERSON><PERSON><PERSON><PERSON>: {path}", "path_not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON>ờng Dẫn: {path}", "modify_file_failed": "<PERSON><PERSON><PERSON> Tệp <PERSON>: {error}", "windows_machine_id_updated": "Cập <PERSON>hật ID Máy Windows Thành Công", "update_windows_machine_id_failed": "<PERSON><PERSON><PERSON> ID Máy Windows Thất Bại: {error}", "update_windows_machine_guid_failed": "<PERSON><PERSON><PERSON> G<PERSON> Máy Windows Thất Bại: {error}", "file_not_found": "<PERSON><PERSON><PERSON><PERSON>: {path}"}, "register": {"title": "Công Cụ Đă<PERSON> K<PERSON> Cursor", "start": "Bắt đầu quá trình đăng ký...", "handling_turnstile": "<PERSON><PERSON> xử lý xác minh bảo mật...", "retry_verification": "<PERSON><PERSON> thử lại xác minh...", "detect_turnstile": "<PERSON><PERSON> kiểm tra xác minh bảo mật...", "verification_success": "<PERSON><PERSON><PERSON> <PERSON>h b<PERSON>o mật thành công", "starting_browser": "<PERSON><PERSON> mở trình du<PERSON>...", "form_success": "Biểu mẫu đã được gửi thành công", "browser_started": "Tr<PERSON>nh duyệt đã được mở thành công", "waiting_for_second_verification": "<PERSON><PERSON> chờ xác minh email...", "waiting_for_verification_code": "<PERSON>ang chờ mã xác minh...", "password_success": "Đặt mật khẩu thành công", "password_error": "<PERSON><PERSON><PERSON><PERSON> thể đặt mật khẩu: {error}. <PERSON><PERSON> lòng thử lại", "waiting_for_page_load": "<PERSON><PERSON> tải trang...", "first_verification_passed": "<PERSON><PERSON><PERSON> minh ban đầu thành công", "mailbox": "<PERSON><PERSON> truy cập hộp thư đến thành công", "register_start": "<PERSON>ắt <PERSON>", "form_submitted": "<PERSON><PERSON><PERSON><PERSON> Mẫu <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>...", "filling_form": "<PERSON><PERSON><PERSON><PERSON> Mẫu", "visiting_url": "<PERSON><PERSON> Truy Cập URL", "basic_info": "Thông Tin Cơ Bản Đã <PERSON>", "handle_turnstile": "<PERSON>ử Lý <PERSON>", "no_turnstile": "<PERSON><PERSON><PERSON><PERSON>ile", "turnstile_passed": "<PERSON><PERSON>", "verification_start": "<PERSON><PERSON><PERSON>", "verification_timeout": "<PERSON><PERSON><PERSON>", "verification_not_found": "<PERSON><PERSON><PERSON><PERSON>", "try_get_code": "Th<PERSON> | {attempt} <PERSON><PERSON><PERSON> | Thời <PERSON>: {time}s", "get_account": "<PERSON><PERSON>", "get_token": "<PERSON><PERSON><PERSON>", "token_success": "<PERSON><PERSON><PERSON>", "token_attempt": "Thử | {attempt} lần để lấy <PERSON> | Sẽ thử lại sau {time}s", "token_max_attempts": "Đạt Số <PERSON>n <PERSON><PERSON> ({max}) | <PERSON>hông <PERSON>h<PERSON>", "token_failed": "<PERSON><PERSON><PERSON>: {error}", "account_error": "<PERSON><PERSON><PERSON> T<PERSON>ấ<PERSON>: {error}", "press_enter": "<PERSON>hấn Enter <PERSON>", "browser_start": "<PERSON><PERSON> Khởi Động <PERSON>", "open_mailbox": "<PERSON><PERSON> Mở <PERSON>", "email_error": "<PERSON><PERSON><PERSON><PERSON> <PERSON>hể <PERSON> Chỉ Email", "setup_error": "Lỗi <PERSON><PERSON><PERSON><PERSON>: {error}", "start_getting_verification_code": "<PERSON><PERSON><PERSON><PERSON>, Sẽ Th<PERSON> Trong 60s", "get_verification_code_timeout": "<PERSON><PERSON><PERSON>", "get_verification_code_success": "<PERSON><PERSON><PERSON>", "try_get_verification_code": "Th<PERSON> | {attempt} <PERSON><PERSON><PERSON> | Thời <PERSON>: {remaining_time}s", "verification_code_filled": "<PERSON><PERSON>", "login_success_and_jump_to_settings_page": "<PERSON><PERSON><PERSON> và Chuyển Đến Trang <PERSON> Đặt", "detect_login_page": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "cursor_registration_completed": "Đăng Ký Cursor <PERSON>!", "set_password": "Đặt <PERSON><PERSON><PERSON>", "basic_info_submitted": "Thông Tin Cơ Bản Đã <PERSON>", "cursor_auth_info_updated": "Thông Tin <PERSON>", "cursor_auth_info_update_failed": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>", "reset_machine_id": "Đặt Lại ID Máy", "account_info_saved": "Thông Tin Tài K<PERSON>ả<PERSON> Đ<PERSON>", "save_account_info_failed": "<PERSON><PERSON><PERSON>ản Thất Bại", "get_email_address": "<PERSON><PERSON><PERSON> Chỉ Email", "update_cursor_auth_info": "<PERSON><PERSON><PERSON>", "register_process_error": "Lỗi <PERSON>u<PERSON>: {error}", "setting_password": "<PERSON><PERSON> Đặt <PERSON>", "manual_code_input": "<PERSON><PERSON><PERSON><PERSON>ô<PERSON>", "manual_email_input": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Họ", "exit_signal": "<PERSON><PERSON>", "email_address": "Đ<PERSON>a Chỉ Email", "config_created": "<PERSON><PERSON> T<PERSON>o <PERSON>", "verification_failed": "<PERSON><PERSON><PERSON>", "verification_error": "Lỗi <PERSON><PERSON><PERSON>: {error}", "config_option_added": "<PERSON><PERSON>êm <PERSON>: {option}", "config_updated": "<PERSON><PERSON><PERSON>", "password_submitted": "<PERSON><PERSON>", "total_usage": "Tổng S<PERSON>: {usage}", "setting_on_password": "<PERSON><PERSON> Đặt <PERSON>", "getting_code": "<PERSON><PERSON>, Sẽ Th<PERSON> Trong 60s", "human_verify_error": "<PERSON><PERSON><PERSON><PERSON> thể xác minh người dùng. <PERSON><PERSON> thử lại...", "max_retries_reached": "<PERSON><PERSON> đạt số lần thử tối đa. <PERSON><PERSON><PERSON> ký thất bại.", "using_tempmail_plus": "Sử dụng Tempmailplus để xác minh email", "tempmail_plus_disabled": "Tempmailplus bị vô hiệu hóa", "tempmail_plus_enabled": "Tempmail<PERSON><PERSON> đ<PERSON><PERSON> bật", "using_browser": "<PERSON><PERSON> dụng tr<PERSON><PERSON> {Browser}: {path}", "tempmail_plus_epin_missing": "Tempmailplus epin không đ<PERSON><PERSON><PERSON> cấu hình", "tempmail_plus_verification_failed": "TempMailplus Xác minh không thành công: {error}", "tracking_processes": "<PERSON> {<PERSON>} {tr<PERSON><PERSON>}", "no_new_processes_detected": "<PERSON><PERSON><PERSON><PERSON> có quy trình {trình duyệt} mới nào đư<PERSON><PERSON> phát hiện để theo dõi", "browser_path_invalid": "{browser} đường dẫn không hợ<PERSON> lệ, sử dụng đường dẫn mặc định", "using_browser_profile": "<PERSON><PERSON> dụng {B<PERSON>er} cấu hình từ: {user_data_dir}", "could_not_track_processes": "<PERSON><PERSON><PERSON><PERSON> thể theo dõi {Browser} Quy trình: {error}", "tempmail_plus_verification_completed": "Tempmailplus Xác minh đã hoàn thành thành công", "tempmail_plus_email_missing": "Email Tempmailplus không đ<PERSON><PERSON><PERSON> cấu hình", "tempmail_plus_config_missing": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> bị thiếu", "tempmail_plus_init_failed": "<PERSON><PERSON><PERSON>ng thể khởi tạo TempMailplus: {Error}", "try_install_browser": "Thử cài đặt trình duyệt với trình quản lý gói của bạn", "tempmail_plus_initialized": "Tempmailplus khởi tạo thành công", "make_sure_browser_is_properly_installed": "<PERSON><PERSON><PERSON> b<PERSON> {trì<PERSON>} đ<PERSON><PERSON><PERSON> cài đặt đúng cách", "tempmail_plus_verification_started": "<PERSON><PERSON><PERSON> đầu quy trình x<PERSON>c minh Tempmailplus"}, "auth": {"title": "<PERSON><PERSON><PERSON><PERSON>", "checking_auth": "<PERSON><PERSON>", "auth_not_found": "<PERSON><PERSON><PERSON><PERSON>", "auth_file_error": "Lỗi Tệ<PERSON>: {error}", "reading_auth": "<PERSON><PERSON>", "updating_auth": "<PERSON><PERSON>", "auth_updated": "<PERSON><PERSON><PERSON>ng <PERSON>", "auth_update_failed": "<PERSON><PERSON><PERSON>ng <PERSON>: {error}", "auth_file_created": "<PERSON><PERSON> T<PERSON>", "auth_file_create_failed": "<PERSON><PERSON><PERSON>: {error}", "press_enter": "<PERSON>hấn Enter <PERSON>", "reset_machine_id": "Đặt Lại ID Máy", "database_connection_closed": "<PERSON><PERSON> Sở D<PERSON>u", "database_updated_successfully": "<PERSON><PERSON><PERSON> Sở <PERSON><PERSON>u T<PERSON>", "connected_to_database": "<PERSON><PERSON>t <PERSON> Sở D<PERSON>u", "updating_pair": "<PERSON><PERSON> Cặp Khóa-Giá <PERSON>rị", "db_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp cơ sở dữ liệu tại: {path}", "db_permission_error": "<PERSON><PERSON><PERSON><PERSON> thể truy cập tệp cơ sở dữ liệu. <PERSON><PERSON> lòng kiểm tra quyền", "db_connection_error": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến cơ sở dữ liệu: {error}"}, "control": {"generate_email": "<PERSON><PERSON>", "blocked_domain": "<PERSON><PERSON><PERSON> Chặn", "select_domain": "<PERSON><PERSON>ên <PERSON> Ngẫu <PERSON>ên", "copy_email": "<PERSON><PERSON> Sao Chép Địa Chỉ Email", "enter_mailbox": "<PERSON><PERSON>", "refresh_mailbox": "<PERSON><PERSON>", "check_verification": "<PERSON><PERSON>", "verification_found": "<PERSON><PERSON><PERSON>", "verification_not_found": "<PERSON><PERSON><PERSON><PERSON>", "browser_error": "Lỗi <PERSON><PERSON><PERSON><PERSON>: {error}", "navigation_error": "Lỗi <PERSON><PERSON><PERSON><PERSON>: {error}", "email_copy_error": "Lỗi Sao Chép Email: {error}", "mailbox_error": "Lỗi <PERSON><PERSON><PERSON>: {error}", "token_saved_to_file": "Token <PERSON><PERSON> cursor_tokens.txt", "navigate_to": "<PERSON><PERSON> {url}", "generate_email_success": "<PERSON><PERSON><PERSON> Email <PERSON>", "select_email_domain": "<PERSON><PERSON><PERSON>", "select_email_domain_success": "<PERSON><PERSON><PERSON>n <PERSON><PERSON>", "get_email_name": "<PERSON><PERSON><PERSON>", "get_email_name_success": "<PERSON><PERSON><PERSON><PERSON>", "get_email_address": "<PERSON><PERSON><PERSON> Chỉ Email", "get_email_address_success": "<PERSON><PERSON><PERSON> Chỉ <PERSON><PERSON>", "enter_mailbox_success": "<PERSON><PERSON><PERSON>", "found_verification_code": "<PERSON><PERSON><PERSON>", "get_cursor_session_token": "<PERSON><PERSON><PERSON>", "get_cursor_session_token_success": "<PERSON><PERSON><PERSON>", "get_cursor_session_token_failed": "<PERSON><PERSON><PERSON>", "save_token_failed": "<PERSON><PERSON><PERSON>", "database_updated_successfully": "<PERSON><PERSON><PERSON> Sở <PERSON><PERSON>u T<PERSON>", "database_connection_closed": "<PERSON><PERSON> Sở D<PERSON>u", "no_valid_verification_code": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"starting_browser": "<PERSON><PERSON> Khởi Động <PERSON>", "visiting_site": "<PERSON><PERSON>", "create_success": "<PERSON><PERSON><PERSON> Email <PERSON>", "create_failed": "<PERSON><PERSON><PERSON>", "create_error": "Lỗi Tạo Email: {error}", "refreshing": "<PERSON><PERSON>", "refresh_success": "<PERSON><PERSON><PERSON>", "refresh_error": "Lỗi <PERSON>à<PERSON>: {error}", "refresh_button_not_found": "<PERSON><PERSON><PERSON><PERSON>", "verification_found": "<PERSON><PERSON> <PERSON><PERSON>", "verification_not_found": "<PERSON><PERSON><PERSON><PERSON>", "verification_error": "Lỗi <PERSON><PERSON><PERSON>: {error}", "verification_code_found": "<PERSON><PERSON><PERSON>", "verification_code_not_found": "<PERSON><PERSON><PERSON><PERSON>", "verification_code_error": "Lỗi <PERSON><PERSON>: {error}", "address": "Đ<PERSON>a Chỉ Email", "all_domains_blocked": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "no_available_domains_after_filtering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switching_service": "<PERSON><PERSON> {service}", "domains_list_error": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>: {error}", "failed_to_get_available_domains": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "domains_excluded": "<PERSON><PERSON><PERSON> Trừ: {domains}", "failed_to_create_account": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>", "account_creation_error": "Lỗi <PERSON><PERSON><PERSON>: {error}", "blocked_domains": "<PERSON><PERSON><PERSON> Bị Chặn: {domains}", "blocked_domains_loaded": "<PERSON><PERSON><PERSON>i Tên <PERSON> Bị Chặn: {count}", "blocked_domains_loaded_error": "Lỗi <PERSON><PERSON><PERSON>ên <PERSON> Bị Chặn: {error}", "blocked_domains_loaded_success": "<PERSON><PERSON><PERSON>ên <PERSON> Bị Chặn Thành <PERSON>", "blocked_domains_loaded_timeout": "<PERSON>ết <PERSON><PERSON><PERSON><PERSON> Tên <PERSON>n Bị Chặn: {timeout}s", "blocked_domains_loaded_timeout_error": "Lỗi Hết T<PERSON><PERSON>i <PERSON> Tên <PERSON>n Bị Chặn: {error}", "available_domains_loaded": "<PERSON><PERSON>n <PERSON>: {count}", "domains_filtered": "<PERSON><PERSON> <PERSON><PERSON>: {count}", "trying_to_create_email": "<PERSON><PERSON>: {email}", "domain_blocked": "<PERSON><PERSON><PERSON> Bị Chặn: {domain}", "using_chrome_profile": "<PERSON><PERSON> Sơ Chrome từ: {user_data_dir}", "no_display_found": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> X Server <PERSON>.", "try_export_display": "Thử: export DISPLAY=:0", "extension_load_error": "Lỗi Tải T<PERSON>n Ích Mở Rộng: {error}", "make_sure_chrome_chromium_is_properly_installed": "<PERSON><PERSON><PERSON>ảo Chrome/Chromium Đượ<PERSON> Đặt <PERSON><PERSON><PERSON>", "try_install_chromium": "Thử: sudo apt install chromium-browser"}, "update": {"title": "<PERSON><PERSON>t Tự <PERSON>", "disable_success": "<PERSON><PERSON>ắt Tự Động <PERSON>p <PERSON>t Th<PERSON>nh <PERSON>", "disable_failed": "<PERSON><PERSON><PERSON>ự <PERSON>ng <PERSON> Th<PERSON><PERSON>: {error}", "press_enter": "<PERSON>hấn Enter <PERSON>", "start_disable": "Bắt Đ<PERSON> Tự Động <PERSON>", "killing_processes": "<PERSON><PERSON>nh", "processes_killed": "<PERSON><PERSON>t <PERSON>nh", "removing_directory": "<PERSON><PERSON>", "directory_removed": "<PERSON><PERSON>", "creating_block_file": "<PERSON><PERSON> Chặn", "block_file_created": "<PERSON><PERSON> Chặn", "clearing_update_yml": "<PERSON><PERSON> Tệp update.yml", "update_yml_cleared": "Đã Xóa Tệp update.yml", "update_yml_not_found": "<PERSON><PERSON><PERSON><PERSON>p update.yml", "clear_update_yml_failed": "Xóa <PERSON> update.yml Thất <PERSON>: {error}", "unsupported_os": "<PERSON><PERSON> Được Hỗ Trợ: {system}", "remove_directory_failed": "<PERSON><PERSON><PERSON>: {error}", "create_block_file_failed": "<PERSON><PERSON><PERSON> Chặn Thất <PERSON>: {error}", "directory_locked": "<PERSON><PERSON><PERSON>: {path}", "yml_locked": "Tệp update.yml <PERSON>", "block_file_locked": "Tệp Chặn <PERSON>", "yml_already_locked": "Tệp update.yml Đã Bị <PERSON>", "block_file_already_locked": "Tệp Chặn Đã B<PERSON>", "block_file_locked_error": "Lỗi <PERSON><PERSON><PERSON><PERSON> Tệp Chặn: {error}", "yml_locked_error": "Lỗi <PERSON><PERSON><PERSON><PERSON> update.yml: {error}", "block_file_already_locked_error": "Lỗi Tệp Chặn Đã B<PERSON>: {error}", "yml_already_locked_error": "Lỗi Tệp update.yml Đã <PERSON>: {error}"}, "updater": {"checking": "<PERSON><PERSON>...", "new_version_available": "<PERSON><PERSON> phiên bản mới! (Hiện tại: {current}, <PERSON><PERSON><PERSON> nhất: {latest})", "updating": "<PERSON><PERSON> cập nhật lên phiên bản mới nhất. Chương trình sẽ tự động khởi động lại.", "up_to_date": "Bạn đang sử dụng phiên bản mới nhất.", "check_failed": "<PERSON><PERSON><PERSON> tra cập nhật thất bại: {error}", "continue_anyway": "<PERSON><PERSON><PERSON><PERSON> tục với phiên bản hiện tại...", "update_confirm": "Bạn có muốn cập nhật lên phiên bản mới nhất không? (Y/n)", "update_skipped": "Bỏ qua cập nhật.", "invalid_choice": "<PERSON><PERSON><PERSON> chọn không hợp lệ. <PERSON><PERSON> lòng nhập 'Y' hoặc 'n'.", "development_version": "<PERSON><PERSON><PERSON> {current} > {latest}", "changelog_title": "<PERSON><PERSON><PERSON><PERSON>", "rate_limit_exceeded": "<PERSON><PERSON> vượt quá giới hạn API GitHub. Bỏ qua kiểm tra cập nhật."}, "totally_reset": {"title": "Đặt Lại <PERSON>", "checking_config": "<PERSON><PERSON><PERSON><PERSON>", "config_not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no_permission": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>ể <PERSON> Hoặc <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON> Tệp", "reading_config": "<PERSON><PERSON>", "creating_backup": "<PERSON><PERSON>", "backup_exists": "Tệ<PERSON>, Bỏ <PERSON><PERSON> Lư<PERSON>", "generating_new_machine_id": "Đang Tạo ID Máy Mới", "saving_new_config": "<PERSON><PERSON> Vào JSON", "success": "Đặt <PERSON><PERSON>i Cursor <PERSON>", "error": "Đặt Lại Cursor <PERSON>: {error}", "press_enter": "<PERSON>hấn Enter <PERSON>", "reset_machine_id": "Đặt Lại ID Máy", "database_connection_closed": "<PERSON><PERSON> Sở D<PERSON>u", "database_updated_successfully": "<PERSON><PERSON><PERSON> Sở <PERSON><PERSON>u T<PERSON>", "connected_to_database": "<PERSON><PERSON>t <PERSON> Sở D<PERSON>u", "updating_pair": "<PERSON><PERSON> Cặp Khóa-Giá <PERSON>rị", "db_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp cơ sở dữ liệu tại: {path}", "db_permission_error": "<PERSON><PERSON><PERSON><PERSON> thể truy cập tệp cơ sở dữ liệu. <PERSON><PERSON> lòng kiểm tra quyền", "db_connection_error": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến cơ sở dữ liệu: {error}", "feature_title": "TÍNH NĂNG", "feature_1": "<PERSON><PERSON>a hoàn toàn cài đặt và cấu hình của Cursor AI", "feature_2": "<PERSON><PERSON><PERSON> tất cả dữ liệu đã lưu trong bộ nhớ cache bao gồ<PERSON> lịch sử AI và lời nhắc", "feature_3": "Đặt lại ID máy để bỏ qua phát hiện dùng thử", "feature_4": "<PERSON><PERSON><PERSON> định danh máy mới ngẫu nhiên", "feature_5": "<PERSON><PERSON><PERSON> tiện ích mở rộng và tùy chọn tùy chỉnh", "feature_6": "Đặt lại thông tin dùng thử và dữ liệu kích ho<PERSON>t", "feature_7": "<PERSON><PERSON><PERSON> sâu các tệp gi<PERSON>y phép và dùng thử ẩn", "feature_8": "<PERSON><PERSON><PERSON> toàn an toàn các tệp và ứng dụng không phải của <PERSON>or", "feature_9": "Tương thích với Windows, macOS và Linux", "disclaimer_title": "TUYÊN BỐ MIỄN TRỪ", "disclaimer_1": "<PERSON><PERSON>ng cụ này sẽ xóa vĩnh viễn tất cả cài đặt Cursor AI,", "disclaimer_2": "cấu hình và dữ liệu đã lưu trong bộ nhớ cache. Hành động này không thể hoàn tác.", "disclaimer_3": "<PERSON><PERSON><PERSON> tệp mã của bạn sẽ KHÔNG bị ảnh hưởng, và công cụ được thiết kế", "disclaimer_4": "chỉ nhắm vào các tệp trình soạn thảo Cursor AI và cơ chế phát hiện dùng thử.", "disclaimer_5": "<PERSON><PERSON><PERSON> <PERSON>ng dụng khác trên hệ thống của bạn sẽ không bị ảnh hưởng.", "disclaimer_6": "Bạn sẽ cần thiết lập lại Cursor AI sau khi chạy công cụ này.", "disclaimer_7": "Sử dụng với rủi ro của riêng bạn", "confirm_title": "Bạn có chắc chắn muốn tiếp tục không?", "confirm_1": "<PERSON><PERSON><PERSON> động này sẽ xóa tất cả cài đặt Cursor AI,", "confirm_2": "cấu hình và dữ liệu đã lưu trong bộ nhớ cache. Hành động này không thể hoàn tác.", "confirm_3": "<PERSON><PERSON><PERSON> tệp mã của bạn sẽ KHÔNG bị ảnh hưởng, và công cụ được thiết kế", "confirm_4": "chỉ nhắm vào các tệp trình soạn thảo Cursor AI và cơ chế phát hiện dùng thử.", "confirm_5": "<PERSON><PERSON><PERSON> <PERSON>ng dụng khác trên hệ thống của bạn sẽ không bị ảnh hưởng.", "confirm_6": "Bạn sẽ cần thiết lập lại Cursor AI sau khi chạy công cụ này.", "confirm_7": "Sử dụng với rủi ro của riêng bạn", "invalid_choice": "<PERSON><PERSON> lòng nhập 'Y' hoặc 'n'", "skipped_for_safety": "Đã bỏ qua vì an toàn (không liên quan đến <PERSON>): {path}", "deleted": "Đ<PERSON> Xóa: {path}", "error_deleting": "Lỗi xóa {path}: {error}", "not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp: {path}", "resetting_machine_id": "<PERSON><PERSON> đặt lại định danh máy để bỏ qua phát hiện dùng thử...", "created_machine_id": "Đã tạo ID máy mới: {path}", "error_creating_machine_id": "Lỗi tạo tệp ID máy {path}: {error}", "error_searching": "Lỗi tìm kiếm tệp trong {path}: {error}", "created_extended_trial_info": "<PERSON><PERSON> tạo thông tin dùng thử mở rộng mới: {path}", "error_creating_trial_info": "Lỗi tạo tệp thông tin dùng thử {path}: {error}", "resetting_cursor_ai_editor": "<PERSON><PERSON> đặt lại Trình soạn thảo Cursor AI... <PERSON><PERSON> lòng đợi.", "reset_cancelled": "<PERSON><PERSON> hủy đặt lại. <PERSON><PERSON><PERSON><PERSON> mà không thay đổi gì.", "windows_machine_id_modification_skipped": "Đã bỏ qua sửa đổi ID máy Windows: {error}", "linux_machine_id_modification_skipped": "Đã bỏ qua sửa đổi machine-id Linux: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Lưu ý: Đặt lại ID máy hoàn toàn có thể yêu cầu chạy với quyền quản trị", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Lưu ý: Đặt lại machine-id hệ thống hoàn toàn có thể yêu cầu quyền sudo", "windows_registry_instructions": "📝 LƯU Ý: Để đặt lại hoàn toàn trên <PERSON>, bạn có thể cần xóa các mục đăng ký.", "windows_registry_instructions_2": "   Chạy 'regedit' và tìm kiếm các khóa chứa 'Cursor' hoặc 'CursorAI' trong HKEY_CURRENT_USER\\Software\\ và xóa chúng.", "reset_log_1": "Cursor AI đã được đặt lại hoàn toàn và bỏ qua phát hiện dùng thử!", "reset_log_2": "<PERSON><PERSON> lòng khởi động lại hệ thống để các thay đổi có hiệu lực.", "reset_log_3": "Bạn sẽ cần cài đặt lại Cursor AI và bây giờ sẽ có một giai đoạn dùng thử mới.", "reset_log_4": "<PERSON><PERSON> có kết quả tốt nhất, h<PERSON><PERSON> xem xét:", "reset_log_5": "Sử dụng một địa chỉ email khác khi đăng ký dùng thử mới", "reset_log_6": "<PERSON><PERSON><PERSON> có thể, sử dụng VPN để thay đổi địa chỉ IP của bạn", "reset_log_7": "Xóa cookie và bộ nhớ cache trình duyệt trư<PERSON><PERSON> khi truy cập trang web Cursor AI", "reset_log_8": "<PERSON><PERSON><PERSON> vấn đề vẫn còn, h<PERSON><PERSON> thử cài đặt Cursor AI ở một vị trí khác", "reset_log_9": "<PERSON><PERSON><PERSON> bạn gặp bất kỳ vấn đề nào, h<PERSON><PERSON> truy cập Github Issue Tracker và tạo một vấn đề tại https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "<PERSON><PERSON> xảy ra lỗi không mong đợi: {error}", "report_issue": "<PERSON><PERSON> lòng báo cáo vấn đề này tới Github Issue Tracker tại https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "<PERSON><PERSON><PERSON> trình bị người dùng ngắt. <PERSON><PERSON> tho<PERSON>...", "return_to_main_menu": "Đang trở về menu chính...", "process_interrupted": "<PERSON><PERSON><PERSON> trình bị ng<PERSON>t. <PERSON><PERSON>o<PERSON>...", "press_enter_to_return_to_main_menu": "Nhấn Enter để trở về menu chính...", "removing_known": "<PERSON><PERSON> x<PERSON>a các tệp dùng thử/gi<PERSON>y phép đã biết", "performing_deep_scan": "<PERSON><PERSON> thực hiện quét sâu để tìm thêm tệp dùng thử/gi<PERSON>y phép", "found_additional_potential_license_trial_files": "<PERSON><PERSON> tìm thấy {count} tệp giấy phép/dùng thử tiềm năng bổ sung", "checking_for_electron_localstorage_files": "<PERSON><PERSON> kiểm tra các tệp localStorage của Electron", "no_additional_license_trial_files_found_in_deep_scan": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp giấy phép/dùng thử bổ sung trong quét sâu", "removing_electron_localstorage_files": "Đang x<PERSON>a các tệp localStorage của Electron", "electron_localstorage_files_removed": "Đã xóa các tệp localStorage của Electron", "electron_localstorage_files_removal_error": "Lỗi xóa các tệp localStorage của Electron: {error}", "electron_localstorage_files_removal_completed": "<PERSON><PERSON><PERSON> tất x<PERSON>a các tệp localStorage của Electron", "warning_title": "CẢNH BÁO", "warning_1": "<PERSON><PERSON><PERSON> động này sẽ xóa tất cả cài đặt Cursor AI,", "warning_2": "cấu hình và dữ liệu đã lưu trong bộ nhớ cache. Hành động này không thể hoàn tác.", "warning_3": "<PERSON><PERSON><PERSON> tệp mã của bạn sẽ KHÔNG bị ảnh hưởng, và công cụ được thiết kế", "warning_4": "chỉ nhắm vào các tệp trình soạn thảo Cursor AI và cơ chế phát hiện dùng thử.", "warning_5": "<PERSON><PERSON><PERSON> <PERSON>ng dụng khác trên hệ thống của bạn sẽ không bị ảnh hưởng.", "warning_6": "Bạn sẽ cần thiết lập lại Cursor AI sau khi chạy công cụ này.", "warning_7": "Sử dụng với rủi ro của riêng bạn", "removed": "Đ<PERSON> Xóa: {path}", "failed_to_reset_machine_guid": "<PERSON><PERSON><PERSON>ng thể đặt lại GUID máy", "failed_to_remove": "<PERSON><PERSON><PERSON><PERSON> thể xóa: {path}", "failed_to_delete_file": "<PERSON><PERSON><PERSON><PERSON> thể xóa tệp: {path}", "failed_to_delete_directory": "<PERSON><PERSON><PERSON><PERSON> thể xóa thư mục: {path}", "failed_to_delete_file_or_directory": "<PERSON><PERSON><PERSON><PERSON> thể xóa tệp hoặc thư mục: {path}", "deep_scanning": "<PERSON><PERSON> thực hiện quét sâu để tìm thêm tệp dùng thử/gi<PERSON>y phép", "resetting_cursor": "<PERSON><PERSON> đặt lại Trình soạn thảo Cursor AI... <PERSON><PERSON> lòng đợi.", "completed_in": "<PERSON><PERSON><PERSON> thành trong {time} gi<PERSON>y", "cursor_reset_completed": "Trì<PERSON> soạn thảo Cursor AI đã được đặt lại hoàn toàn và bỏ qua phát hiện dùng thử!", "cursor_reset_failed": "Đặt lại Trình soạn thảo Cursor AI thất bại: {error}", "cursor_reset_cancelled": "<PERSON><PERSON> hủy đặt lại Trình soạn thảo Cursor AI. <PERSON><PERSON><PERSON><PERSON> mà không thay đổi gì.", "operation_cancelled": "<PERSON><PERSON> hủy thao tác. <PERSON><PERSON><PERSON><PERSON> mà không thay đổi gì.", "navigating_to_settings": "<PERSON><PERSON> điều hướng đến trang cài đặt...", "already_on_settings": "Đã ở trang cài đặt", "login_redirect_failed": "<PERSON><PERSON><PERSON><PERSON> hướng đăng nhập thấ<PERSON> b<PERSON>, đang thử điều hướng trự<PERSON> tiếp...", "advanced_tab_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tab <PERSON>âng cao sau nhiều lần thử", "advanced_tab_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tab <PERSON><PERSON> cao, lần thử {attempt}/{max_attempts}", "advanced_tab_error": "Lỗi tìm tab <PERSON>âng cao: {error}", "advanced_tab_clicked": "Đã nhấp vào tab Nâng cao", "direct_advanced_navigation": "<PERSON><PERSON> thử điều hướng trự<PERSON> tiếp đến tab nâng cao", "delete_button_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nút <PERSON>a Tài khoản sau nhiều lần thử", "delete_button_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy n<PERSON>, lần thử {attempt}/{max_attempts}", "delete_button_error": "Lỗi tìm nút <PERSON>: {error}", "delete_button_clicked": "Đã nhấp vào nút Xóa Tài <PERSON>n", "found_danger_zone": "<PERSON><PERSON> tìm thấy phần V<PERSON><PERSON> hi<PERSON>", "delete_input_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ô nhập xác nhận xóa sau nhiều lần thử", "delete_input_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ô nhập xóa, lần thử {attempt}/{max_attempts}", "delete_input_error": "Lỗi tìm ô nhập <PERSON>: {error}", "delete_input_not_found_continuing": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ô nhập xác nhận xóa, đang thử tiếp tục", "removing_electron_localstorage_files_completed": "<PERSON><PERSON> hoàn thành việc loại bỏ các tệp địa phương của Electron"}, "github_register": {"title": "Tự Đ<PERSON>ng Hóa <PERSON> Ký GitHub + Cursor AI", "features_header": "<PERSON><PERSON><PERSON>", "feature1": "Tạo email tạm thời sử dụng 1secmail.", "feature2": "<PERSON><PERSON><PERSON> ký tài khoản GitHub mới với thông tin ngẫu nhiên.", "feature3": "Tự động x<PERSON>c <PERSON>h email GitHub.", "feature4": "<PERSON><PERSON><PERSON> nh<PERSON>p v<PERSON>o Cursor AI sử dụng xác thực <PERSON>.", "feature5": "Đặt lại ID máy để bỏ qua phát hiện dùng thử.", "feature6": "<PERSON><PERSON><PERSON> tất cả thông tin đăng nhập vào tệp.", "warnings_header": "<PERSON><PERSON><PERSON>", "warning1": "<PERSON>ript này tự động hóa việc tạo tài <PERSON>, có thể vi phạm điều khoản dịch vụ của <PERSON>/Cursor.", "warning2": "<PERSON><PERSON><PERSON> c<PERSON>u truy cập internet và quyền quản trị.", "warning3": "CAPTCHA hoặc x<PERSON><PERSON> minh bổ sung có thể làm gián đoạn tự động hóa.", "warning4": "<PERSON><PERSON> dụng có trách nhiệm và tự chịu rủi ro.", "confirm": "Bạn có chắc chắn muốn tiếp tục không?", "invalid_choice": "<PERSON><PERSON><PERSON> chọn không hợp lệ. <PERSON><PERSON> lòng nhập 'yes' hoặc 'no'", "cancelled": "<PERSON><PERSON> hủy thao tác", "program_terminated": "<PERSON><PERSON><PERSON><PERSON> trình bị người dùng chấm dứt", "starting_automation": "B<PERSON>t đầu tự động hóa...", "github_username": "<PERSON><PERSON><PERSON>", "github_password": "<PERSON><PERSON><PERSON>", "email_address": "Đ<PERSON>a Chỉ Email", "credentials_saved": "<PERSON><PERSON><PERSON> thông tin đăng nhập này đã đư<PERSON><PERSON> lưu vào github_cursor_accounts.txt", "completed_successfully": "<PERSON><PERSON><PERSON> ký GitHub + <PERSON><PERSON><PERSON> hoàn tất thành công!", "registration_encountered_issues": "<PERSON><PERSON><PERSON> ký GitHub + <PERSON>ursor gặp vấn đề.", "check_browser_windows_for_manual_intervention_or_try_again_later": "<PERSON><PERSON><PERSON> tra cửa sổ trình duyệt để can thiệp thủ công hoặc thử lại sau."}, "account_info": {"subscription": "<PERSON><PERSON><PERSON>", "trial_remaining": "Thờ<PERSON> G<PERSON>ng Thử Pro Còn Lại", "days": "ng<PERSON>y", "subscription_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đăng ký", "email_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy email", "failed_to_get_account": "<PERSON><PERSON>ông thể lấy thông tin tài khoản", "config_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cấu hình.", "failed_to_get_usage": "<PERSON><PERSON><PERSON>ng thể lấy thông tin sử dụng", "failed_to_get_subscription": "<PERSON><PERSON><PERSON>ng thể lấy thông tin đăng ký", "failed_to_get_email": "<PERSON><PERSON><PERSON><PERSON> thể lấy địa chỉ email", "failed_to_get_token": "<PERSON><PERSON><PERSON><PERSON> thể lấy token", "failed_to_get_account_info": "<PERSON><PERSON>ông thể lấy thông tin tài khoản", "title": "Thông Tin T<PERSON>", "email": "Email", "token": "Token", "usage": "<PERSON><PERSON>", "subscription_type": "<PERSON><PERSON><PERSON>", "remaining_trial": "Thờ<PERSON>", "days_remaining": "Số Ngà<PERSON>", "premium": "Premium", "pro": "Pro", "pro_trial": "<PERSON><PERSON><PERSON>", "team": "Team", "enterprise": "Enterprise", "free": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON>", "inactive": "Không Hoạt Động", "premium_usage": "Sử Dụng Premium", "basic_usage": "Sử Dụ<PERSON>", "usage_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin sử dụng", "lifetime_access_enabled": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "token_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token"}, "config": {"config_not_available": "<PERSON><PERSON><PERSON><PERSON> có sẵn cấu hình", "configuration": "<PERSON><PERSON><PERSON>", "enabled": "Đã Bật", "disabled": "Đã Tắt", "config_directory": "<PERSON><PERSON><PERSON>", "neither_cursor_nor_cursor_directory_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON>ursor hoặc thư mục Cursor trong {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "<PERSON>ui lòng đảm bảo Cursor đã được cài đặt và đã chạy ít nhất một lần", "storage_directory_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư mục lưu trữ: {storage_dir}", "storage_file_found": "<PERSON><PERSON> tìm thấy tệp lưu trữ: {storage_path}", "file_size": "<PERSON><PERSON><PERSON> th<PERSON> tệp: {size} bytes", "file_permissions": "<PERSON><PERSON><PERSON><PERSON> tệp: {permissions}", "file_owner": "Ch<PERSON> sở hữu tệp: {owner}", "file_group": "<PERSON><PERSON><PERSON><PERSON> tệp: {group}", "error_getting_file_stats": "Lỗi lấy thông tin tệp: {error}", "permission_denied": "Từ chối quyền: {storage_path}", "try_running": "<PERSON>h<PERSON> chạy: {command}", "and": "Và", "storage_file_is_empty": "<PERSON><PERSON><PERSON> lưu trữ trống: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "<PERSON><PERSON><PERSON> có thể bị hỏng, vui lòng cài đặt lại <PERSON>or", "storage_file_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp lưu trữ: {storage_path}", "error_checking_linux_paths": "Lỗi kiểm tra đường dẫn Linux: {error}", "config_option_added": "<PERSON><PERSON> thêm tùy chọn cấu hình: {option}", "config_updated": "<PERSON><PERSON> cập nhật cấu hình", "config_created": "<PERSON><PERSON> tạo cấu hình: {config_file}", "config_setup_error": "Lỗi thiết lập cấu hình: {error}", "storage_file_is_valid_and_contains_data": "<PERSON><PERSON><PERSON> lưu trữ hợp lệ và chứa dữ liệu", "error_reading_storage_file": "Lỗi đọc tệp lưu trữ: {error}", "also_checked": "<PERSON><PERSON><PERSON> đã kiểm tra {path}", "backup_created": "<PERSON><PERSON> tạo bản sao lưu: {path}", "config_removed": "<PERSON><PERSON> xóa tệp cấu hình để cập nhật b<PERSON><PERSON> buộc", "backup_failed": "<PERSON><PERSON> l<PERSON>u cấu hình thất bại: {error}", "force_update_failed": "<PERSON><PERSON><PERSON> nh<PERSON>t b<PERSON><PERSON> buộc cấu hình thất bại: {error}", "config_force_update_disabled": "<PERSON><PERSON> tắt cập nhật bắt buộc tệp cấu hình, bỏ qua cập nhật bắt buộc", "config_force_update_enabled": "<PERSON><PERSON> bật cập nhật bắt buộc tệp cấu hình, thực hiện cập nhật bắt buộc", "documents_path_not_found": "Đường dẫn tài liệu không đư<PERSON><PERSON> tìm thấy, sử dụng thư mục hiện tại", "using_temp_dir": "<PERSON><PERSON> dụng thư mục tạm thời do lỗi: {path} (lỗi: {error})", "config_dir_created": "<PERSON><PERSON><PERSON> mục cấu hình đã tạo: {path}"}, "oauth": {"authentication_button_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nút xác thực", "authentication_failed": "<PERSON><PERSON><PERSON> thực thất bại: {error}", "found_cookies": "<PERSON><PERSON> tìm thấy {count} cookie", "token_extraction_error": "Lỗi trích xuất token: {error}", "authentication_successful": "<PERSON><PERSON><PERSON> thực thành công - Email: {email}", "missing_authentication_data": "<PERSON><PERSON><PERSON>u dữ liệu xác thực: {data}", "failed_to_delete_account": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài k<PERSON>n: {error}", "invalid_authentication_type": "<PERSON><PERSON><PERSON> x<PERSON>c thực không hợp lệ", "auth_update_success": "<PERSON><PERSON><PERSON> nhật x<PERSON>c thực thành công", "browser_closed": "<PERSON><PERSON> đóng trình <PERSON>", "auth_update_failed": "<PERSON><PERSON><PERSON> nhật xác thực thất bại", "google_start": "Bắt đ<PERSON>u Google", "github_start": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usage_count": "Số lần sử dụng: {usage}", "account_has_reached_maximum_usage": "<PERSON><PERSON><PERSON> khoản đã đạt số lần sử dụng tối đa, {deleting}", "starting_new_authentication_process": "B<PERSON>t đầu quá trình xác thực mới...", "failed_to_delete_expired_account": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài kho<PERSON>n hết hạn", "could_not_check_usage_count": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra số lần sử dụng: {error}", "found_email": "<PERSON><PERSON> tìm thấy email: {email}", "could_not_find_email": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy email: {error}", "could_not_find_usage_count": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy số lần sử dụng: {error}", "already_on_settings_page": "Đã ở trang cài đặt!", "failed_to_extract_auth_info": "<PERSON><PERSON>ông thể trích xuất thông tin xác thực: {error}", "no_chrome_profiles_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON> hồ s<PERSON>, sử dụng Mặc định", "found_default_chrome_profile": "<PERSON><PERSON> tìm thấy hồ sơ Chrome Mặc định", "using_first_available_chrome_profile": "<PERSON><PERSON> dụng hồ sơ Chrome khả dụng đầu tiên: {profile}", "error_finding_chrome_profile": "Lỗi tì<PERSON> hồ sơ <PERSON>, sử dụng Mặc định: {error}", "initializing_browser_setup": "<PERSON><PERSON> khởi tạo thiết lập trình du<PERSON>...", "detected_platform": "<PERSON><PERSON> phát hiện nền tảng: {platform}", "running_as_root_warning": "Chạy với quyền root không đư<PERSON><PERSON> khuyến nghị cho tự động hóa trình du<PERSON>t", "consider_running_without_sudo": "Hãy xem xét chạy script mà không cần sudo", "no_compatible_browser_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trình duyệt tương thích. <PERSON><PERSON> lòng cài đặt Google Chrome hoặc Chromium.", "supported_browsers": "Tr<PERSON><PERSON> được hỗ trợ cho {platform}", "using_browser_profile": "<PERSON><PERSON> sử dụ<PERSON> hồ sơ trình du<PERSON>t: {profile}", "starting_browser": "<PERSON><PERSON> khởi động trình duyệt tại: {path}", "browser_setup_completed": "<PERSON><PERSON><PERSON><PERSON> lập trình duyệt hoàn tất thành công", "browser_setup_failed": "<PERSON><PERSON><PERSON><PERSON> lập trình duyệt thất bại: {error}", "try_running_without_sudo_admin": "Th<PERSON> chạy mà không cần quyền sudo/quản trị", "redirecting_to_authenticator_cursor_sh": "<PERSON><PERSON> chuyển hướng đến authenticator.cursor.sh...", "starting_google_authentication": "B<PERSON>t đ<PERSON>u x<PERSON>c thực Google...", "starting_github_authentication": "<PERSON><PERSON><PERSON> đ<PERSON>u x<PERSON>c thực <PERSON>...", "waiting_for_authentication": "<PERSON><PERSON> chờ xác thực...", "page_changed_checking_auth": "<PERSON>rang đã thay đổi, đang kiểm tra xác thực...", "status_check_error": "Lỗi kiểm tra trạng thái: {error}", "authentication_timeout": "<PERSON><PERSON><PERSON> thời gian x<PERSON>c thực", "account_is_still_valid": "<PERSON><PERSON><PERSON> k<PERSON>n vẫn còn hợp lệ (Sử dụng: {usage})", "starting_re_authentication_process": "B<PERSON>t đầu quá trình xác thực lại...", "starting_new_google_authentication": "B<PERSON>t đầu x<PERSON>c thực Google mới...", "failed_to_delete_account_or_re_authenticate": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài khoản hoặc xác thực lại: {error}", "navigating_to_authentication_page": "<PERSON><PERSON> điều hướng đến trang xác thực...", "please_select_your_google_account_to_continue": "<PERSON><PERSON> lòng chọn tà<PERSON> Google của bạn để tiếp tục...", "found_browser_data_directory": "<PERSON><PERSON> tìm thấy thư mục dữ liệu trình du<PERSON>: {path}", "authentication_successful_getting_account_info": "<PERSON><PERSON><PERSON> thực thành công, đang lấy thông tin tài k<PERSON>n...", "warning_could_not_kill_existing_browser_processes": "<PERSON><PERSON><PERSON> báo: <PERSON><PERSON><PERSON><PERSON> thể kết thúc các tiến trình trình duyệt hiện có: {error}", "browser_failed_to_start": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>t không thể khởi động: {error}", "browser_failed": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>t không thể khởi động: {error}", "browser_failed_to_start_fallback": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>t không thể khởi động: {error}", "warning_browser_close": "CẢNH BÁO: <PERSON><PERSON><PERSON><PERSON> này sẽ đóng tất cả các quy trình đang chạy {trình duyệt}", "profile_selection_error": "Lỗi trong quá trình lựa chọn hồ sơ: {error}", "using_configured_browser_path": "<PERSON><PERSON> dụng cấu hình {Browser} đường dẫn: {path}", "found_chrome_at": "<PERSON><PERSON><PERSON> thấy Chrome tại: {path}", "killing_browser_processes": "<PERSON><PERSON><PERSON><PERSON> {trì<PERSON>} quy trình ...", "browser_not_found_trying_chrome": "<PERSON><PERSON><PERSON>ng thể tìm thấy {trình duyệt}, thay vào đó thử Chrome", "error_getting_user_data_directory": "Lỗi nhận được thư mục dữ liệu người dùng: {error}", "found_browser_user_data_dir": "<PERSON><PERSON><PERSON> thấy {Browser} <PERSON><PERSON><PERSON> mục dữ liệu người dùng: {Path}", "user_data_dir_not_found": "{Browser} thư mục dữ liệu người dùng không tìm thấy tại {path}, thay vào đó sẽ thử Chrome"}, "chrome_profile": {"title": "<PERSON><PERSON>n <PERSON>ơ Chrome", "select_profile": "<PERSON><PERSON><PERSON> mộ<PERSON> hồ sơ Chrome để sử dụng:", "profile_list": "<PERSON><PERSON> sơ khả dụng:", "default_profile": "<PERSON><PERSON> Sơ Mặc Định", "profile": "<PERSON><PERSON> {number}", "no_profiles": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON> hồ sơ Chrome", "error_loading": "Lỗi tải hồ sơ Chrome: {error}", "profile_selected": "<PERSON><PERSON> chọn hồ sơ: {profile}", "invalid_selection": "<PERSON><PERSON><PERSON> chọn không hợp lệ. <PERSON><PERSON> lòng thử lại", "warning_chrome_close": "Cảnh báo: <PERSON><PERSON><PERSON><PERSON> này sẽ đóng tất cả các tiến trình Chrome đang chạy"}, "account_delete": {"title": "Công Cụ Xóa Tài Khoản Google Cursor", "warning": "CẢNH BÁO: <PERSON><PERSON><PERSON><PERSON> này sẽ xóa vĩnh viễn tài khoản Cursor của bạn. Hành động này không thể hoàn tác.", "cancelled": "<PERSON><PERSON> hủy xóa tài <PERSON>.", "starting_process": "B<PERSON>t đầu quá trình xóa tài k<PERSON>n...", "google_button_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nút đăng nhập Google", "logging_in": "<PERSON><PERSON> đăng nhập bằng Google...", "waiting_for_auth": "<PERSON><PERSON> chờ xác thực Google...", "login_successful": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "unexpected_page": "<PERSON><PERSON> không mong đợi sau khi đăng nhập: {url}", "trying_settings": "<PERSON><PERSON> thử điều hướng đến trang cài đặt...", "select_google_account": "<PERSON><PERSON> lòng chọn tà<PERSON> Google của bạn...", "auth_timeout": "<PERSON><PERSON><PERSON> thời gian <PERSON> thực, vẫn tiếp tục...", "navigating_to_settings": "<PERSON><PERSON> điều hướng đến trang cài đặt...", "already_on_settings": "Đã ở trang cài đặt", "login_redirect_failed": "<PERSON><PERSON><PERSON><PERSON> hướng đăng nhập thấ<PERSON> b<PERSON>, đang thử điều hướng trự<PERSON> tiếp...", "advanced_tab_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tab <PERSON>âng cao sau nhiều lần thử", "advanced_tab_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tab <PERSON><PERSON> cao, lần thử {attempt}/{max_attempts}", "advanced_tab_error": "Lỗi tìm tab <PERSON>âng cao: {error}", "advanced_tab_clicked": "Đã nhấp vào tab Nâng cao", "direct_advanced_navigation": "<PERSON><PERSON> thử điều hướng trự<PERSON> tiếp đến tab nâng cao", "delete_button_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nút <PERSON>a Tài khoản sau nhiều lần thử", "delete_button_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy n<PERSON>, lần thử {attempt}/{max_attempts}", "delete_button_error": "Lỗi tìm nút <PERSON>: {error}", "delete_button_clicked": "Đã nhấp vào nút Xóa Tài <PERSON>n", "found_danger_zone": "<PERSON><PERSON> tìm thấy phần V<PERSON><PERSON> hi<PERSON>", "delete_input_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ô nhập xác nhận xóa sau nhiều lần thử", "delete_input_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ô nhập xóa, lần thử {attempt}/{max_attempts}", "delete_input_error": "Lỗi tìm ô nhập <PERSON>: {error}", "delete_input_not_found_continuing": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ô nhập xác nhận xóa, đang thử tiếp tục", "typed_delete": "<PERSON><PERSON> nhập \"Delete\" vào ô xác nhận", "confirm_button_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nút <PERSON> nhận sau nhiều lần thử", "confirm_button_retry": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nút <PERSON>, lần thử {attempt}/{max_attempts}", "confirm_button_error": "Lỗi tìm nút <PERSON> nhận: {error}", "account_deleted": "Đã xóa tài khoản thành công!", "error": "Lỗi trong quá trình xóa tài khoản: {error}", "success": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>n <PERSON>or của bạn đã đư<PERSON><PERSON> xóa thành công!", "failed": "<PERSON>u<PERSON> trình xóa tài khoản thất bại hoặc đã bị hủy.", "interrupted": "<PERSON><PERSON><PERSON> trình xóa tài kho<PERSON>n bị người dùng ng<PERSON>t.", "unexpected_error": "Lỗi không mong đợi: {error}", "found_email": "<PERSON><PERSON> tìm thấy email: {email}", "email_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy email: {error}", "confirm_prompt": "Bạn có chắc chắn muốn tiếp tục không? (y/N): "}, "bypass": {"starting": "<PERSON><PERSON><PERSON> đầu bỏ qua phiên bản <PERSON>ursor...", "found_product_json": "Đã tìm thấy product.json: {path}", "no_write_permission": "<PERSON><PERSON><PERSON><PERSON> có quyền ghi cho tệp: {path}", "read_failed": "<PERSON><PERSON><PERSON>ng thể đọc product.json: {error}", "current_version": "<PERSON><PERSON><PERSON> bản hiện tại: {version}", "backup_created": "<PERSON><PERSON> tạo bản sao lưu: {path}", "version_updated": "<PERSON><PERSON> cập nhật phiên bản từ {old} lên {new}", "write_failed": "Không thể ghi product.json: {error}", "no_update_needed": "<PERSON><PERSON><PERSON><PERSON> cần cập nhật. <PERSON><PERSON><PERSON> bản hiện tại {version} đã >= 0.46.0", "bypass_failed": "Bỏ qua phiên bản thất bại: {error}", "stack_trace": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> ng<PERSON>n xếp", "localappdata_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy biến môi trường LOCALAPPDATA", "product_json_not_found": "Không tìm thấy product.json trong các đường dẫn Linux thông thường", "unsupported_os": "<PERSON><PERSON> điều hành không được hỗ trợ: {system}", "file_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp: {path}", "title": "<PERSON><PERSON>ng <PERSON> Bỏ <PERSON>ua <PERSON>", "description": "Công cụ này sửa đổi product.json của Cursor để bỏ qua hạn chế phiên bản", "menu_option": "Bỏ <PERSON><PERSON>"}, "auth_check": {"checking_authorization": "<PERSON><PERSON> kiểm tra quyền...", "token_source": "Lấy token từ cơ sở dữ liệu hay nhập thủ công? (d/m, mặc định: d)", "getting_token_from_db": "<PERSON><PERSON> lấy token từ cơ sở dữ liệu...", "token_found_in_db": "<PERSON>ã tìm thấy token trong cơ sở dữ liệu", "token_not_found_in_db": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token trong cơ sở dữ liệu", "cursor_acc_info_not_found": "<PERSON><PERSON><PERSON>ng tìm thấy cursor_acc_info.py", "error_getting_token_from_db": "Lỗi lấy token từ cơ sở dữ liệu: {error}", "enter_token": "<PERSON>hập token <PERSON><PERSON><PERSON> c<PERSON>a bạn: ", "token_length": "Độ dài token: {length} ký tự", "usage_response_status": "<PERSON>r<PERSON><PERSON> thái phản hồi sử dụng: {response}", "unexpected_status_code": "Mã trạng thái không mong đợi: {code}", "jwt_token_warning": "Token có vẻ ở định dạng JWT, nhưng kiểm tra API trả về mã trạng thái không mong đợi. Token có thể hợp lệ nhưng truy cập API bị hạn chế.", "invalid_token": "<PERSON><PERSON> không hợp lệ", "user_authorized": "<PERSON><PERSON><PERSON><PERSON> dùng đ<PERSON><PERSON><PERSON> quyền", "user_unauthorized": "<PERSON><PERSON><PERSON><PERSON> dùng không đ<PERSON><PERSON><PERSON> <PERSON><PERSON> quyền", "request_timeout": "<PERSON><PERSON><PERSON> c<PERSON>u hết thời gian", "connection_error": "Lỗi kết nối", "check_error": "Lỗi kiểm tra quyền: {error}", "authorization_successful": "Ủy quyền thành công!", "authorization_failed": "Ủy quyền thất bại!", "operation_cancelled": "<PERSON><PERSON> t<PERSON> bị người dùng hủy", "unexpected_error": "Lỗi không mong đợi: {error}", "error_generating_checksum": "Lỗi tạo checksum: {error}", "checking_usage_information": "<PERSON><PERSON> kiểm tra thông tin sử dụng...", "check_usage_response": "<PERSON><PERSON><PERSON> hồi kiểm tra sử dụng: {response}", "usage_response": "<PERSON><PERSON><PERSON> hồi sử dụng: {response}"}, "restore": {"title": "<PERSON><PERSON>ô<PERSON> phục ID máy từ bản sao lưu", "starting": "<PERSON>ắt đầu quá trình khôi phục ID máy", "no_backups_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bản sao lưu nào", "available_backups": "<PERSON><PERSON><PERSON> bản sao lưu có sẵn", "select_backup": "<PERSON><PERSON><PERSON> bản sao lưu để khôi phục", "to_cancel": "<PERSON><PERSON> hủy", "operation_cancelled": "<PERSON><PERSON> hủy thao tác", "invalid_selection": "<PERSON><PERSON><PERSON> ch<PERSON>n không hợp lệ", "please_enter_number": "<PERSON><PERSON> lòng nhập một số hợp lệ", "missing_id": "Thiếu ID: {id}", "read_backup_failed": "<PERSON><PERSON><PERSON><PERSON> thể đọc tệp sao lưu: {error}", "current_file_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp lưu trữ hiện tại", "current_backup_created": "<PERSON><PERSON> tạo bản sao lưu của tệp lưu trữ hiện tại", "storage_updated": "<PERSON><PERSON><PERSON> lưu trữ đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "update_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật tệp lưu trữ: {error}", "sqlite_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cơ sở dữ liệu SQLite", "updating_sqlite": "<PERSON><PERSON> cập nhật cơ sở dữ liệu SQLite", "updating_pair": "<PERSON><PERSON> cập nhật cặp khóa-giá trị", "sqlite_updated": "<PERSON>ơ sở dữ liệu SQLite đã được cập nhật thành công", "sqlite_update_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật cơ sở dữ liệu SQLite: {error}", "machine_id_backup_created": "Đ<PERSON> tạo bản sao lưu của tệp machineId", "backup_creation_failed": "<PERSON><PERSON><PERSON><PERSON> thể tạo bản sao lưu: {error}", "machine_id_updated": "<PERSON><PERSON><PERSON> machineId đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "machine_id_update_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật tệp machineId: {error}", "updating_system_ids": "<PERSON><PERSON> cập nh<PERSON><PERSON> <PERSON> hệ thống", "system_ids_update_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nh<PERSON>t ID hệ thống: {error}", "permission_denied": "Quyền truy cập bị từ chối. <PERSON><PERSON> lòng thử chạy với quyền quản trị", "windows_machine_guid_updated": "GUID máy Windows đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "update_windows_machine_guid_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật GUID máy Windows: {error}", "windows_machine_id_updated": "ID máy Windows đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "update_windows_machine_id_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật ID máy Windows: {error}", "sqm_client_key_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khóa đăng ký SQMClient", "update_windows_system_ids_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật ID hệ thống Windows: {error}", "macos_platform_uuid_updated": "UUID nền tảng macOS đã đư<PERSON><PERSON> cập nhật thành công", "failed_to_execute_plutil_command": "<PERSON><PERSON><PERSON><PERSON> thể thực thi lệnh plutil", "update_macos_system_ids_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật ID hệ thống macOS: {error}", "ids_to_restore": "<PERSON> máy c<PERSON>n kh<PERSON>i phục", "confirm": "Bạn có chắc chắn muốn khôi phục những ID này không?", "success": "ID máy đã đư<PERSON><PERSON> khôi phục thành công", "process_error": "Lỗi quá trình khôi phục: {error}", "press_enter": "<PERSON><PERSON><PERSON><PERSON> Enter để tiếp tục"}, "manual_auth": {"auth_type_selected": "<PERSON><PERSON><PERSON> xác thực đã chọn: {type}", "verifying_token": "<PERSON><PERSON><PERSON> <PERSON>h t<PERSON>h hợp lệ của mã thông báo ...", "token_prompt": "<PERSON><PERSON><PERSON><PERSON> mã thông báo con trỏ của bạn (access_token/refresh_token):", "title": "<PERSON><PERSON><PERSON> thực con trỏ thủ công", "proceed_prompt": "Ti<PERSON><PERSON> tục? (y/n):", "auth_updated_successfully": "Thông tin xác thực đư<PERSON>c cập nhật thành công!", "continue_anyway": "Ti<PERSON><PERSON> tục dù sao? (y/n):", "token_verified": "Mã thông báo đã xác minh thành công!", "email_prompt": "Nhập email (để trống cho email ngẫu nhiên):", "token_verification_skipped": "<PERSON><PERSON><PERSON> <PERSON>h mã thông báo đã bỏ qua (Check_user_authorized.py không tìm thấy)", "auth_type_github": "GitHub", "random_email_generated": "Email ngẫu nhiên đ<PERSON> tạo: {email}", "error": "Lỗi: {lỗi}", "confirm_prompt": "<PERSON><PERSON> lòng xác nhận thông tin sau:", "invalid_token": "<PERSON><PERSON> thông báo không hợp lệ. <PERSON><PERSON><PERSON> thực bị hủy bỏ.", "auth_update_failed": "<PERSON><PERSON><PERSON><PERSON> cập nhật thông tin xác thực", "token_required": "<PERSON><PERSON> thông báo là bắt buộc", "auth_type_google": "Google", "auth_type_prompt": "<PERSON><PERSON><PERSON> xác thực:", "operation_cancelled": "<PERSON><PERSON><PERSON> động bị hủy bỏ", "auth_type_auth0": "Auth_0 (mặc định)", "token_verification_error": "Lỗi xác minh mã thông báo: {error}", "updating_database": "<PERSON><PERSON><PERSON> nh<PERSON>t cơ sở dữ liệu xác thực con trỏ ..."}, "token": {"refreshing": "<PERSON><PERSON>m mới mã thông báo ...", "extraction_error": "<PERSON><PERSON><PERSON><PERSON> xuất lỗi mã thông báo: {error}", "request_timeout": "<PERSON><PERSON><PERSON> c<PERSON>u làm mới máy chủ đã hết thời gian", "connection_error": "Lỗi kết nối để làm mới máy chủ", "unexpected_error": "Lỗi không mong muốn trong quá trình làm mới mã thông báo: {error}", "server_error": "<PERSON><PERSON><PERSON> mới lỗi máy chủ: http {status}", "invalid_response": "<PERSON><PERSON><PERSON> hồi JSON không hợp lệ từ máy chủ làm mới", "no_access_token": "<PERSON><PERSON><PERSON><PERSON> có mã thông báo truy cập để đáp <PERSON>ng", "refresh_failed": "<PERSON><PERSON><PERSON> mới mã thông báo không thành công: {error}", "refresh_success": "Mã thông báo làm mới thành công! Hợ<PERSON> lệ cho {ngày} ngày (hết hạn: {hết hạn})"}, "bypass_token_limit": {"press_enter": "<PERSON><PERSON><PERSON>n Enter để tiếp tục ...", "title": "Bỏ qua công cụ giới hạn mã thông báo", "description": "C<PERSON>ng cụ này sửa đổi tệp workbench.desktop.main.js để bỏ qua giới hạn mã thông báo"}, "browser_profile": {"profile_selected": "<PERSON><PERSON> sơ đã chọn: {hồ sơ}", "default_profile": "<PERSON><PERSON> sơ mặc định", "no_profiles": "<PERSON><PERSON><PERSON><PERSON> tìm thấy {trình du<PERSON>}", "select_profile": "<PERSON><PERSON><PERSON> {Browser} <PERSON><PERSON> sơ để sử dụng:", "error_loading": "<PERSON><PERSON><PERSON> lỗi {<PERSON>rowser} <PERSON><PERSON><PERSON> hình: {error}", "title": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> hồ sơ trình du<PERSON>t", "profile": "<PERSON><PERSON> sơ {Number}", "profile_list": "<PERSON><PERSON> sẵn {tr<PERSON><PERSON>} Hồ sơ:", "invalid_selection": "<PERSON><PERSON><PERSON> chọn không hợp lệ. <PERSON><PERSON><PERSON> thử lại."}, "tempmail": {"config_error": "Lỗi tệp cấu hình: {error}", "general_error": "<PERSON><PERSON> xảy ra lỗi: {lỗi}", "no_email": "<PERSON><PERSON><PERSON><PERSON> tìm thấy email xác minh con trỏ", "checking_email": "<PERSON><PERSON><PERSON> tra email xác minh con trỏ ...", "configured_email": "<PERSON><PERSON> đ<PERSON><PERSON> đ<PERSON>nh cấu hình: {email}", "extract_code_failed": "<PERSON><PERSON><PERSON><PERSON> xuất mã xác minh không thành công: {error}", "no_code": "<PERSON><PERSON><PERSON><PERSON> thể nhận mã x<PERSON>c minh", "check_email_failed": "<PERSON><PERSON><PERSON> tra email không thành công: {lỗi}", "email_found": "<PERSON><PERSON><PERSON> thấy email xác minh con trỏ", "verification_code": "Mã xác minh: {code}"}}